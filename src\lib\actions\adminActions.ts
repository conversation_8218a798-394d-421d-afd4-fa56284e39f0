"use server";

import { analyzeFraudRisk, type AnalyzeFraudRiskInput, type AnalyzeFraudRiskOutput } from "@/ai/flows/analyze-fraud-risk";
import type { Order, VPSPlan } from "@/types";
import { getVpsPlanById, getAllVpsPlans } from "@/lib/actions/vpsPlanActions";
import { readJsonFile, writeJsonFile } from '@/lib/jsonUtils';

const ORDERS_FILE = 'orders.json';

async function getOrderDetailsForFraudAnalysis(orderId: string): Promise<AnalyzeFraudRiskInput | null> {
  const orders = await readJsonFile<Order>(ORDERS_FILE);
  const order = orders.find(o => o.id === orderId);

  if (!order) {
    return null;
  }

  const vpsPlanInfo: VPSPlan | undefined = await getVpsPlanById(order.planId);
  const vpsPlanName = vpsPlanInfo ? vpsPlanInfo.name : order.planId;

  const userOrderHistory = orders
    .filter(o => o.userId === order.userId && o.id !== orderId)
    .map(o => o.id);

  return {
    userData: {
      userId: order.userId,
      email: order.userEmail || "<EMAIL>",
      billingAddress: order.billingAddress || "N/A",
      paymentMethod: order.paymentMethod,
      orderHistory: userOrderHistory,
    },
    orderData: {
      orderId: order.id,
      vpsPlan: vpsPlanName,
      orderDate: order.orderDate,
      ipAddress: order.ipAddress || "N/A",
      totalAmount: order.totalAmount,
    },
  };
}

export async function performFraudCheck(orderId: string): Promise<AnalyzeFraudRiskOutput | { error: string }> {
  try {
    const inputData = await getOrderDetailsForFraudAnalysis(orderId);
    if (!inputData) {
      return { error: "Order not found or insufficient data for fraud check." };
    }

    const analysisResult = await analyzeFraudRisk(inputData);

    const orders = await readJsonFile<Order>(ORDERS_FILE);
    const orderIndex = orders.findIndex(o => o.id === orderId);

    if (orderIndex !== -1) {
      orders[orderIndex].fraudAnalysis = { ...analysisResult, analyzedAt: new Date().toISOString() };
      await writeJsonFile<Order>(ORDERS_FILE, orders);
    } else {
      console.warn(`Order ID ${orderId} not found in orders.json after fraud check for persistence.`);
    }
    return analysisResult;
  } catch (error) {
    console.error("Error in performFraudCheck:", error);
    const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred during fraud analysis.";
    return { error: errorMessage };
  }
}

export async function getOrdersForFraudReview(): Promise<Order[]> {
  const orders = await readJsonFile<Order>(ORDERS_FILE);
  const vpsPlans = await getAllVpsPlans();

  return orders.map(order => {
    const planDetails = vpsPlans.find(p => p.id === order.planId);
    return {
      ...order,
      vpsPlanName: planDetails ? planDetails.name : order.planId,
    };
  }).sort((a, b) => new Date(b.orderDate).getTime() - new Date(a.orderDate).getTime());
}

export async function updateOrderStatus(orderId: string, newStatus: Order['status'], newVpsStatus?: Order['vpsStatus']): Promise<{ success: boolean, error?: string, order?: Order }> {
  try {
    const orders = await readJsonFile<Order>(ORDERS_FILE);
    const orderIndex = orders.findIndex(o => o.id === orderId);
    if (orderIndex === -1) {
      return { success: false, error: "Order not found." };
    }
    orders[orderIndex].status = newStatus;
    if (newVpsStatus !== undefined) {
      orders[orderIndex].vpsStatus = newVpsStatus;
    }

    if (newVpsStatus === undefined) {
      if (newStatus === 'active' && (!orders[orderIndex].vpsStatus || orders[orderIndex].vpsStatus === 'provisioning')) {
        orders[orderIndex].vpsStatus = 'running';
        if (!orders[orderIndex].operatingSystem) orders[orderIndex].operatingSystem = "Ubuntu 22.04 LTS";
        if (!orders[orderIndex].ipAddress) orders[orderIndex].ipAddress = `192.168.1.${100 + orderIndex}`;
      } else if (newStatus === 'cancelled' || newStatus === 'fraud_review') {
        orders[orderIndex].vpsStatus = 'suspended';
      } else if (newStatus === 'processing' && (orders[orderIndex].status === 'pending' || orders[orderIndex].status === 'fraud_review')) {
        orders[orderIndex].vpsStatus = 'provisioning';
      }
    }

    await writeJsonFile<Order>(ORDERS_FILE, orders);
    console.log(`Order ${orderId} status updated to ${newStatus}, VPS status to ${orders[orderIndex].vpsStatus}.`);
    return { success: true, order: orders[orderIndex] };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : "Failed to update order status.";
    console.error("Error updating order status:", error);
    return { success: false, error: errorMessage };
  }
}

export async function adminGetAllVpsInstances(): Promise<Order[]> {
  const orders = await readJsonFile<Order>(ORDERS_FILE);
  const vpsPlans = await getAllVpsPlans();

  return orders.map(order => {
    const planDetails = vpsPlans.find(p => p.id === order.planId);
    return {
      ...order,
      vpsPlanName: planDetails ? planDetails.name : order.planId,
    };
  }).sort((a, b) => new Date(b.orderDate).getTime() - new Date(a.orderDate).getTime());
}

export async function adminUpdateVpsStatus(
  orderId: string,
  newVpsStatus: Order['vpsStatus']
): Promise<{ success: boolean; error?: string; order?: Order }> {
  try {
    const orders = await readJsonFile<Order>(ORDERS_FILE);
    const orderIndex = orders.findIndex(o => o.id === orderId);

    if (orderIndex === -1) {
      return { success: false, error: 'Order not found.' };
    }

    const oldStatus = orders[orderIndex].vpsStatus;
    orders[orderIndex].vpsStatus = newVpsStatus;

    if (oldStatus === 'suspended' && (newVpsStatus === 'running' || newVpsStatus === 'stopped')) {
      if (orders[orderIndex].status === 'fraud_review' || orders[orderIndex].status === 'cancelled') {
        orders[orderIndex].status = 'active';
      }
    }

    if (newVpsStatus === 'rebooting') {
      setTimeout(async () => {
        const currentOrders = await readJsonFile<Order>(ORDERS_FILE);
        const currentOrderIndex = currentOrders.findIndex(o => o.id === orderId);
        if (currentOrderIndex !== -1 && currentOrders[currentOrderIndex].vpsStatus === 'rebooting') {
          currentOrders[currentOrderIndex].vpsStatus = 'running';
          await writeJsonFile<Order>(ORDERS_FILE, currentOrders);
          console.log(`Admin: VPS ${orderId} rebooted and set to running.`);
        }
      }, 3000);
    }

    await writeJsonFile<Order>(ORDERS_FILE, orders);
    return { success: true, order: orders[orderIndex] };
  } catch (error) {
    console.error('Error updating VPS status by admin:', error);
    return { success: false, error: 'Failed to update VPS status.' };
  }
}
