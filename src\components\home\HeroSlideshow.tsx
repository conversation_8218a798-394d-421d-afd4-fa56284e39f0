"use client";

import type { ReactNode, CSSProperties } from 'react';
import { useState, useEffect, useCallback } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { cn } from '@/lib/utils';

// This is the type for the RAW data coming from homepage-slides.json
export interface RawSlideData {
  id: string;
  titleConfig: {
    text: string;
    element?: string; // e.g., 'h1', 'h2'
    className?: string;
    style?: CSSProperties;
    highlight?: {
      text: string;
      className?: string;
      position: 'before' | 'after';
    };
  };
  description: string;
  cta1?: { text: string; href: string; variant?: 'default' | 'outline' | 'secondary' };
  cta2?: { text: string; href: string; variant?: 'default' | 'outline' | 'secondary' };
  backgroundClasses?: string; // e.g., "bg-gradient-to-r from-blue-500 to-purple-600"
  contentAlignment?: 'center' | 'left' | 'right';
}

// This is the type for the PROCESSED slide data, where title is ReactNode
export interface SlideContent extends Omit<RawSlideData, 'titleConfig' | 'backgroundImageUrl' | 'imageHint'> {
  title: ReactNode; // Title is now expected to be ReactNode (JSX)
  backgroundClasses?: string;
  cta1?: { text: string; href: string; variant?: 'default' | 'outline' | 'secondary' };
  cta2?: { text: string; href: string; variant?: 'default' | 'outline' | 'secondary' };
  contentAlignment?: 'center' | 'left' | 'right';
  id: string;
  description: string;
}


interface HeroSlideshowProps {
  slides: SlideContent[];
  autoplayInterval?: number; // in milliseconds
}

export function HeroSlideshow({ slides, autoplayInterval = 7000 }: HeroSlideshowProps) {
  const [currentIndex, setCurrentIndex] = useState(0);

  const goToPrevious = useCallback(() => {
    setCurrentIndex((prevIndex) => (prevIndex === 0 ? slides.length - 1 : prevIndex - 1));
  }, [slides.length]);

  const goToNext = useCallback(() => {
    setCurrentIndex((prevIndex) => (prevIndex === slides.length - 1 ? 0 : prevIndex + 1));
  }, [slides.length]);

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  useEffect(() => {
    if (slides.length <= 1 || !autoplayInterval) return;

    const intervalId = setInterval(goToNext, autoplayInterval);
    return () => clearInterval(intervalId);
  }, [goToNext, autoplayInterval, slides.length]);

  if (!slides || slides.length === 0) {
    return <div className="text-center py-16 text-muted-foreground">No slides to display.</div>;
  }

  return (
    <div className="relative w-full aspect-[16/7] md:aspect-[16/6] lg:aspect-[16/5] overflow-hidden rounded-lg shadow-lg group">
      {/* Slides */}
      {slides.map((slide, index) => (
        <div
          key={slide.id}
          className={cn(
            "absolute inset-0 transition-opacity duration-1000 ease-in-out flex items-center",
            index === currentIndex ? "opacity-100 z-10" : "opacity-0 z-0",
            slide.backgroundClasses || "bg-slate-700"
          )}
        >
          <div className={cn(
            "relative z-10 container mx-auto px-4 text-white flex",
            "py-4 sm:py-6 md:py-8 lg:py-10", // Responsive vertical padding
            slide.contentAlignment === 'left' ? 'justify-start text-left' :
              slide.contentAlignment === 'right' ? 'justify-end text-right' :
                'justify-center text-center'
          )}>
            <div>
              {slide.title}
              <p className="text-sm md:text-base mb-4 text-shadow-md">
                {slide.description}
              </p>
              <div className={cn(
                "space-x-0 sm:space-x-4", // Remove horizontal space on mobile for buttons to stack
                slide.contentAlignment === 'left' ? 'justify-start' :
                  slide.contentAlignment === 'right' ? 'justify-end' :
                    'justify-center',
                "flex flex-col sm:flex-row items-center sm:items-start flex-wrap gap-2" // Stack buttons on mobile
              )}>
                {slide.cta1 && (
                  <Button size="default" variant={slide.cta1.variant || 'secondary'} asChild className="shadow-md hover:shadow-lg transition-shadow w-full sm:w-auto">
                    <Link href={slide.cta1.href}>{slide.cta1.text}</Link>
                  </Button>
                )}
                {slide.cta2 && (
                  <Button size="default" variant={slide.cta2.variant || 'outline'} asChild className="shadow-md hover:shadow-lg transition-shadow bg-white/20 backdrop-blur-sm border-white/50 hover:bg-white/30 w-full sm:w-auto">
                    <Link href={slide.cta2.href}>{slide.cta2.text}</Link>
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      ))}

      {/* Navigation Buttons */}
      {slides.length > 1 && (
        <>
          <Button
            variant="outline"
            size="icon"
            onClick={goToPrevious}
            className="absolute top-1/2 left-2 sm:left-4 -translate-y-1/2 z-20 bg-black/30 hover:bg-black/50 text-white border-white/50 opacity-0 group-hover:opacity-100 transition-opacity"
            aria-label="Previous slide"
          >
            <ChevronLeft className="h-6 w-6" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={goToNext}
            className="absolute top-1/2 right-2 sm:right-4 -translate-y-1/2 z-20 bg-black/30 hover:bg-black/50 text-white border-white/50 opacity-0 group-hover:opacity-100 transition-opacity"
            aria-label="Next slide"
          >
            <ChevronRight className="h-6 w-6" />
          </Button>
        </>
      )}

      {/* Dot Indicators */}
      {slides.length > 1 && (
        <div className="absolute bottom-4 sm:bottom-6 left-1/2 -translate-x-1/2 z-20 flex space-x-2">
          {slides.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              aria-label={`Go to slide ${index + 1}`}
              className={cn(
                "h-2 w-2 sm:h-3 sm:w-3 rounded-full transition-colors",
                index === currentIndex ? "bg-white" : "bg-white/50 hover:bg-white/75"
              )}
            />
          ))}
        </div>
      )}
    </div>
  );
}

