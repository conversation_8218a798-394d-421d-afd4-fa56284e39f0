'use server';

import type { User } from '@/types';
import { readJsonFile, writeJsonFile } from '@/lib/jsonUtils';

interface UserWithPassword extends User {
  password?: string;
}

const USERS_FILE = 'users.json';

export async function getAllUsers(): Promise<User[]> {
  const usersWithPasswords = await readJsonFile<UserWithPassword>(USERS_FILE);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  return usersWithPasswords.map(({ password, ...userWithoutPassword }) => userWithoutPassword);
}

export async function updateUserStatus(userId: string, newStatus: User['status']): Promise<{ user?: User, error?: string }> {
  let users = await readJsonFile<UserWithPassword>(USERS_FILE);
  const userIndex = users.findIndex(u => u.id === userId);

  if (userIndex === -1) {
    return { error: 'User not found.' };
  }

  users[userIndex].status = newStatus;
  await writeJsonFile<UserWithPassword>(USERS_FILE, users);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { password, ...updatedUser } = users[userIndex];
  return { user: updatedUser };
}

export async function updateUserRole(userId: string, newRole: User['role']): Promise<{ user?: User, error?: string }> {
  let users = await readJsonFile<UserWithPassword>(USERS_FILE);
  const userIndex = users.findIndex(u => u.id === userId);

  if (userIndex === -1) {
    return { error: 'User not found.' };
  }

  users[userIndex].role = newRole;
  await writeJsonFile<UserWithPassword>(USERS_FILE, users);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { password, ...updatedUser } = users[userIndex];
  return { user: updatedUser };
}

export async function deleteUserInJson(userId: string): Promise<{ success?: boolean, error?: string }> {
  let users = await readJsonFile<UserWithPassword>(USERS_FILE);
  const initialLength = users.length;
  users = users.filter(u => u.id !== userId);

  if (users.length === initialLength) {
    return { error: 'User not found for deletion.' };
  }

  await writeJsonFile<UserWithPassword>(USERS_FILE, users);
  return { success: true };
}

export async function updateUser(userId: string, updates: { name?: string; email?: string }): Promise<{ user?: User, error?: string }> {
  let users = await readJsonFile<UserWithPassword>(USERS_FILE);
  const userIndex = users.findIndex(u => u.id === userId);

  if (userIndex === -1) {
    return { error: 'User not found.' };
  }

  if (updates.email && users.some(u => u.email === updates.email && u.id !== userId)) {
    return { error: 'This email address is already in use by another account.' };
  }

  users[userIndex] = {
    ...users[userIndex],
    ...updates,
  };

  await writeJsonFile<UserWithPassword>(USERS_FILE, users);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { password, ...updatedUserWithoutPassword } = users[userIndex];
  return { user: updatedUserWithoutPassword };
}
