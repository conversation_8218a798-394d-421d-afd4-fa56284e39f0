import React, { useState } from "react";

interface DemoLoginRowProps {
  email: string;
  password: string;
}

const copyToClipboard = async (text: string): Promise<boolean> => {
  // Modern Clipboard API - works in secure contexts (HTTPS, localhost)
  if (navigator.clipboard) {
    try {
      await navigator.clipboard.writeText(text);
      return true;
    } catch (error) {
      console.warn('Clipboard API failed:', error);
      // Continue to fallback
    }
  }

  // Fallback for older browsers or insecure contexts
  // This approach doesn't use deprecated APIs
  try {
    // Create a temporary input element
    const input = document.createElement('input');
    input.value = text;
    input.style.position = 'fixed';
    input.style.left = '-999999px';
    input.style.top = '-999999px';
    input.style.opacity = '0';
    input.setAttribute('readonly', '');

    document.body.appendChild(input);

    // Select the text
    input.select();
    input.setSelectionRange(0, 99999); // For mobile devices

    // Create a custom event to simulate copy
    // This is a more modern approach than execCommand
    let successful = false;

    // Try to trigger a copy event manually
    const copyEvent = new ClipboardEvent('copy', {
      clipboardData: new DataTransfer()
    });

    if (copyEvent.clipboardData) {
      copyEvent.clipboardData.setData('text/plain', text);
      successful = document.dispatchEvent(copyEvent);
    }

    document.body.removeChild(input);
    return successful;
  } catch (error) {
    console.warn('Fallback copy method failed:', error);
    return false;
  }
};

const CopyButton: React.FC<{ value: string; label: string }> = ({ value, label }) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (await copyToClipboard(value)) {
      setCopied(true);
      setTimeout(() => setCopied(false), 1200);
    }
  };

  return (
    <button
      onClick={handleCopy}
      className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity text-gray-400 hover:text-gray-700"
      title={`Copy ${label}`}
      aria-label={`Copy ${label}`}
      type="button"
    >
      {copied ? (
        // Green check mark SVG
        <svg width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="#22c55e">
          <path d="M5 13l4 4L19 7" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
      ) : (
        // Modern, stylish copy icon
        <svg width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.2" strokeLinecap="round" strokeLinejoin="round">
          <rect x="9" y="9" width="10" height="10" rx="2.5" />
          <path d="M5 15V5a2 2 0 0 1 2-2h10" />
        </svg>
      )}
    </button>
  );
};

export const DemoLoginRow: React.FC<DemoLoginRowProps> = ({ email, password }) => (
  <div className="relative group text-left p-4 border border-gray-200 rounded-md bg-gray-50 cursor-pointer text-sm hover:bg-gray-100 transition-all">
    <div className="flex items-center justify-between">
      <span>
        Email - <span className="font-mono">{email}</span>
      </span>
      <CopyButton value={email} label="email" />
    </div>
    <div className="flex items-center justify-between mt-2">
      <span>
        Password - <span className="font-mono">{password}</span>
      </span>
      <CopyButton value={password} label="password" />
    </div>
  </div>
);
