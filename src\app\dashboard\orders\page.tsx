"use client";

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import type { Order } from "@/types";
import { Eye, MoreVertical, Trash2, ShoppingBag, Loader2, Power, Play, RotateCcw, CreditCard as PaymentIcon, CalendarDays, IndianRupee, Globe, Settings as OsIcon } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Link from "next/link";
import { useAuth } from '@/hooks/useAuth';
import { getUserOrders, updateOrderStatusInJson, updateUserVpsStatus } from '@/lib/actions/orderActions';
import { useToast } from '@/hooks/use-toast';
import { Skeleton } from '@/components/ui/skeleton';

export default function OrdersPage() {
  const { user, isAuthenticated } = useAuth();
  const { toast } = useToast();
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedOrderForDialog, setSelectedOrderForDialog] = useState<Order | null>(null);
  const [updatingVpsId, setUpdatingVpsId] = useState<string | null>(null);

  const fetchUserOrders = async () => {
    if (isAuthenticated && user?.id) {
      setIsLoading(true);
      try {
        const fetchedOrders = await getUserOrders(user.id);
        setOrders(fetchedOrders);
      } catch (error) {
        console.error("Failed to fetch orders:", error);
        toast({
          title: "Error",
          description: "Could not load your orders.",
          variant: "error",
        });
        setOrders([]);
      } finally {
        setIsLoading(false);
      }
    } else if (isAuthenticated === false) {
      setIsLoading(false);
      setOrders([]);
    }
  };

  useEffect(() => {
    fetchUserOrders();
  }, [user, isAuthenticated]);

  const getStatusVariant = (status: Order['status']) => {
    switch (status) {
      case 'active': return 'default';
      case 'pending': return 'secondary';
      case 'cancelled': return 'destructive';
      case 'fraud_review': return 'outline';
      case 'processing': return 'secondary';
      default: return 'outline';
    }
  };

  const getVpsStatusVariant = (status?: Order['vpsStatus']) => {
    switch (status) {
      case 'running': return 'default';
      case 'stopped': return 'destructive';
      case 'rebooting': return 'secondary';
      case 'suspended': return 'destructive';
      case 'provisioning': return 'secondary';
      case 'error': return 'destructive';
      default: return 'outline';
    }
  };

  const handleViewDetails = (order: Order) => {
    setSelectedOrderForDialog(order);
  };

  const handleCancelOrder = async (orderId: string) => {
    const result = await updateOrderStatusInJson(orderId, 'cancelled');
    if(result.success && result.order) {
        setOrders(prevOrders => prevOrders.map(o => o.id === orderId ? result.order! : o));
        toast({title: "Order Cancelled", description: `Order ${orderId} has been cancelled.`, variant: "success"});
    } else {
        toast({title: "Cancellation Failed", description: result.error || "Could not cancel order.", variant: "error"});
    }
  };

  const handleVpsAction = async (orderId: string, action: Order['vpsStatus']) => {
    if (!user?.id || !action) return;
    setUpdatingVpsId(orderId);
    const result = await updateUserVpsStatus(orderId, action, user.id);
    setUpdatingVpsId(null);

    if (result.success && result.order) {
      setOrders(prevOrders => prevOrders.map(o => o.id === orderId ? { ...o, ...result.order } : o));
      toast({ title: "VPS Action Success", description: `VPS ${orderId} is now ${action}.`, variant: "success" });
      if (action === 'rebooting') {
        setTimeout(() => fetchUserOrders(), 4000);
      }
    } else {
      toast({ title: "VPS Action Failed", description: result.error || "Could not perform action.", variant: "error" });
    }
  };

  const renderSkeletonOrderCards = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {[...Array(4)].map((_, i) => (
        <Card key={i} className="shadow-md flex flex-col">
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <Skeleton className="h-5 w-3/4 mb-1" />
                <Skeleton className="h-3 w-1/2" />
              </div>
              <Skeleton className="h-8 w-8 rounded-sm" />
            </div>
          </CardHeader>
          <CardContent className="space-y-3 text-sm flex-grow">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-baseline">
              <Skeleton className="h-4 w-1/4 mb-1 sm:mb-0" />
              <Skeleton className="h-4 w-1/2" />
            </div>
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-baseline">
              <Skeleton className="h-4 w-1/5 mb-1 sm:mb-0" />
              <Skeleton className="h-4 w-2/3" />
            </div>
             <div className="flex flex-col sm:flex-row sm:justify-between sm:items-baseline">
              <Skeleton className="h-4 w-1/3 mb-1 sm:mb-0" />
              <Skeleton className="h-4 w-1/3" />
            </div>
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-baseline">
              <Skeleton className="h-4 w-1/4 mb-1 sm:mb-0" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          </CardContent>
          <CardFooter className="border-t pt-4 flex flex-col items-start gap-3 sm:flex-row sm:justify-between sm:items-center sm:gap-4">
            <Skeleton className="h-6 w-24" />
            <Skeleton className="h-6 w-20" />
          </CardFooter>
        </Card>
      ))}
    </div>
  );


  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <h1 className="text-3xl font-bold text-primary">My Services</h1>
          <Skeleton className="h-10 w-36" />
        </div>
        {renderSkeletonOrderCards()}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h1 className="text-3xl font-bold text-primary">My Services</h1>
        <Button asChild>
          <Link href="/vps-offerings">Order New Service</Link>
        </Button>
      </div>

      {orders.length === 0 ? (
        <Card className="shadow-md">
            <CardContent className="text-center py-12">
              <ShoppingBag className="mx-auto h-16 w-16 text-muted-foreground mb-6" />
              <h3 className="text-xl font-semibold text-primary mb-3">No Services Yet</h3>
              <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                It looks like you haven't ordered any services. Explore our VPS plans to find the perfect fit for your needs and get started today!
              </p>
              <Button asChild size="lg">
                <Link href="/vps-offerings">Explore Plans & Order</Link>
              </Button>
            </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {orders.map((order: Order) => (
            <Card key={order.id} className="shadow-md flex flex-col">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg font-semibold text-primary">
                      {order.vpsPlanName || order.planId}
                    </CardTitle>
                    <CardDescription className="text-xs">
                      Order ID: <span className="font-mono">{order.id}</span>
                    </CardDescription>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8" disabled={updatingVpsId === order.id}>
                        {updatingVpsId === order.id ? <Loader2 className="h-4 w-4 animate-spin" /> : <MoreVertical className="h-4 w-4" />}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleViewDetails(order)}>
                        <Eye className="mr-2 h-4 w-4" /> View Details
                      </DropdownMenuItem>
                      {order.status === 'pending' && (
                          <DropdownMenuItem asChild>
                            <Link href={`/payment/upi?orderId=${order.id}&planId=${order.planId}`} className="flex items-center">
                              <PaymentIcon className="mr-2 h-4 w-4" /> Complete Payment
                            </Link>
                          </DropdownMenuItem>
                        )}

                      {order.status === 'active' && order.vpsStatus === 'running' && (
                        <DropdownMenuItem onClick={() => handleVpsAction(order.id, 'stopped')} disabled={updatingVpsId === order.id}>
                          <Power className="mr-2 h-4 w-4" /> Stop Server
                        </DropdownMenuItem>
                      )}
                      {order.status === 'active' && order.vpsStatus === 'stopped' && (
                        <DropdownMenuItem onClick={() => handleVpsAction(order.id, 'running')} disabled={updatingVpsId === order.id}>
                          <Play className="mr-2 h-4 w-4" /> Start Server
                        </DropdownMenuItem>
                      )}
                      {order.status === 'active' && (order.vpsStatus === 'running' || order.vpsStatus === 'stopped') && (
                        <DropdownMenuItem onClick={() => handleVpsAction(order.id, 'rebooting')} disabled={updatingVpsId === order.id}>
                          <RotateCcw className="mr-2 h-4 w-4" /> Reboot Server
                        </DropdownMenuItem>
                      )}

                      {order.status !== 'cancelled' && (
                        <DropdownMenuItem className="text-destructive focus:text-destructive-foreground focus:bg-destructive" onClick={() => handleCancelOrder(order.id)}>
                            <Trash2 className="mr-2 h-4 w-4" /> Cancel Order
                        </DropdownMenuItem>
                        )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent className="space-y-3 text-sm flex-grow">
                <div className="flex flex-col text-left sm:flex-row sm:justify-between sm:items-baseline">
                  <span className="text-sm text-muted-foreground flex items-center"><Globe className="mr-2 h-4 w-4" />IP:</span>
                  <span className="text-sm font-medium break-words font-mono text-xs">{order.ipAddress || 'N/A'}</span>
                </div>
                <div className="flex flex-col text-left sm:flex-row sm:justify-between sm:items-baseline">
                  <span className="text-sm text-muted-foreground flex items-center"><OsIcon className="mr-2 h-4 w-4" />OS:</span>
                  <span className="text-sm font-medium break-words">{order.operatingSystem || 'N/A'}</span>
                </div>
                 <div className="flex flex-col text-left sm:flex-row sm:justify-between sm:items-baseline">
                  <span className="text-sm text-muted-foreground flex items-center"><IndianRupee className="mr-2 h-4 w-4" />Amount:</span>
                  <span className="text-sm font-medium break-words">₹{order.totalAmount.toFixed(2)}</span>
                </div>
                <div className="flex flex-col text-left sm:flex-row sm:justify-between sm:items-baseline">
                  <span className="text-sm text-muted-foreground flex items-center"><CalendarDays className="mr-2 h-4 w-4" />Ordered:</span>
                  <span className="text-sm font-medium break-words">{new Date(order.orderDate).toLocaleDateString()}</span>
                </div>
              </CardContent>
              <CardFooter className="border-t pt-4 flex flex-col items-start gap-3 sm:flex-row sm:justify-between sm:items-center sm:gap-4">
                <div>
                  <span className="text-xs text-muted-foreground mr-1">Order Status: </span>
                  <Badge variant={getStatusVariant(order.status)} className="capitalize text-xs">
                    {order.status.replace('_', ' ')}
                  </Badge>
                </div>
                <div>
                  <span className="text-xs text-muted-foreground mr-1">VPS Status: </span>
                  {order.vpsStatus ? (
                    <Badge variant={getVpsStatusVariant(order.vpsStatus)} className="capitalize text-xs">
                      {order.vpsStatus.replace('_', ' ')}
                      {updatingVpsId === order.id && <Loader2 className="ml-1 h-3 w-3 animate-spin"/>}
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="text-xs">N/A</Badge>
                  )}
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}

      {selectedOrderForDialog && (
        <Dialog open={!!selectedOrderForDialog} onOpenChange={(isOpen) => { if (!isOpen) setSelectedOrderForDialog(null); }}>
          <DialogContent className="sm:max-w-lg">
            <DialogHeader>
              <DialogTitle className="text-primary">Order Details - {selectedOrderForDialog.id}</DialogTitle>
              <DialogDescription>
                Full information for your service.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4 text-sm">
              <div className="grid grid-cols-[150px_1fr] items-center gap-x-4 gap-y-2">
                <Label className="text-right text-muted-foreground">Order ID:</Label>
                <p className="font-medium">{selectedOrderForDialog.id}</p>

                <Label className="text-right text-muted-foreground">Plan Name:</Label>
                <p>{selectedOrderForDialog.vpsPlanName || selectedOrderForDialog.planId}</p>

                <Label className="text-right text-muted-foreground">Order Date:</Label>
                <p>{new Date(selectedOrderForDialog.orderDate).toLocaleString()}</p>

                <Label className="text-right text-muted-foreground">Total Amount:</Label>
                <p>₹{selectedOrderForDialog.totalAmount.toFixed(2)}</p>

                <Label className="text-right text-muted-foreground">Order Status:</Label>
                <div><Badge variant={getStatusVariant(selectedOrderForDialog.status)} className="capitalize">{selectedOrderForDialog.status.replace('_', ' ')}</Badge></div>

                <Label className="text-right text-muted-foreground">VPS Status:</Label>
                <div>{selectedOrderForDialog.vpsStatus ? <Badge variant={getVpsStatusVariant(selectedOrderForDialog.vpsStatus)} className="capitalize">{selectedOrderForDialog.vpsStatus}</Badge> : 'N/A'}</div>

                <Label className="text-right text-muted-foreground">Operating System:</Label>
                <p>{selectedOrderForDialog.operatingSystem || 'N/A'}</p>

                <Label className="text-right text-muted-foreground">IP Address:</Label>
                <p className="font-mono text-xs">{selectedOrderForDialog.ipAddress || 'N/A'}</p>

                <Label className="text-right text-muted-foreground">Payment Method:</Label>
                <p>{selectedOrderForDialog.paymentMethod}</p>

                {selectedOrderForDialog.billingAddress && (
                  <>
                    <Label className="text-right text-muted-foreground">Billing Address:</Label>
                    <p className="whitespace-pre-wrap break-words">{selectedOrderForDialog.billingAddress}</p>
                  </>
                )}

                {selectedOrderForDialog.fraudAnalysis?.analyzedAt && (
                  <>
                    <Separator className="col-span-2 my-2" />
                    <Label className="text-right text-muted-foreground font-semibold text-primary" title="Fraud Analysis (Admin View Mock)">Fraud Check:</Label>
                    <p className={`font-semibold ${selectedOrderForDialog.fraudAnalysis.isFraudulent ? 'text-destructive' : 'text-green-600'}`}>
                      {selectedOrderForDialog.fraudAnalysis.isFraudulent ? 'High Risk' : 'Low Risk'}
                    </p>
                    <Label className="text-right text-muted-foreground">Recommendation:</Label>
                    <p>{selectedOrderForDialog.fraudAnalysis.recommendation}</p>
                  </>
                )}
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setSelectedOrderForDialog(null)}>Close</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
