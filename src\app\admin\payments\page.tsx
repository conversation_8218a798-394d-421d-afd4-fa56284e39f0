"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { CreditCard, MoreVertical, CheckCircle, XCircle, Loader2, RefreshCw } from "lucide-react";
import type { Payment } from "@/types";
import { useToast } from '@/hooks/use-toast';
import { getAllPayments, updatePaymentStatus } from '@/lib/actions/paymentActions';
import { updateOrderStatus } from '@/lib/actions/adminActions';
import { Skeleton } from '@/components/ui/skeleton';

export default function AdminPaymentsPage() {
  const [payments, setPayments] = useState<Payment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessingPayment, setIsProcessingPayment] = useState<string | null>(null);
  const { toast } = useToast();

  const fetchAllPayments = async () => {
    setIsLoading(true);
    try {
      const fetchedPayments = await getAllPayments();
      setPayments(fetchedPayments);
    } catch (error) {
      toast({
        title: "Error fetching payments",
        description: error instanceof Error ? error.message : "Could not load payment data.",
        variant: "error",
      });
      setPayments([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchAllPayments();
  }, []);

  const handleUpdatePaymentAndOrder = async (payment: Payment, newPaymentStatus: 'completed' | 'failed') => {
    setIsProcessingPayment(payment.id);

    const paymentResult = await updatePaymentStatus(payment.id, newPaymentStatus);

    if (paymentResult.error || !paymentResult.payment) {
      toast({ title: "Payment Update Failed", description: paymentResult.error || "Could not update payment status.", variant: "error" });
      setIsProcessingPayment(null);
      return;
    }

    toast({
      title: `Payment Status Updated`,
      description: `Payment ${payment.id} is now ${newPaymentStatus}.`,
      variant: newPaymentStatus === 'completed' ? 'success' : 'info',
    });

    if (newPaymentStatus === 'completed' && payment.orderId) {
      const orderUpdateResult = await updateOrderStatus(payment.orderId, 'active', 'running');
      if (orderUpdateResult.success) {
        toast({
          title: `Order ${payment.orderId} Updated`,
          description: `Order status set to 'active' and VPS to 'running'.`,
          variant: "success",
        });
      } else {
        toast({
          title: `Order Update Failed for ${payment.orderId}`,
          description: orderUpdateResult.error || "Could not update associated order.",
          variant: "error",
        });
      }
    }

    setIsProcessingPayment(null);
    fetchAllPayments();
  };

  const getStatusBadgeVariant = (status: Payment['status']) => {
    switch (status) {
      case 'completed': return 'default';
      case 'pending': return 'secondary';
      case 'failed': return 'destructive';
      default: return 'outline';
    }
  };

  const renderSkeletonCards = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {[...Array(4)].map((_, i) => (
        <Card key={i} className="shadow-md flex flex-col">
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <Skeleton className="h-5 w-3/4 mb-1" />
                <Skeleton className="h-3 w-1/2" />
              </div>
              <Skeleton className="h-6 w-16 rounded-full" />
            </div>
          </CardHeader>
          <CardContent className="space-y-3 text-sm flex-grow">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-baseline">
              <Skeleton className="h-4 w-1/4 mb-1 sm:mb-0" />
              <Skeleton className="h-4 w-1/2" />
            </div>
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-baseline">
              <Skeleton className="h-4 w-1/5 mb-1 sm:mb-0" />
              <Skeleton className="h-4 w-2/3" />
            </div>
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-baseline">
              <Skeleton className="h-4 w-1/6 mb-1 sm:mb-0" />
              <Skeleton className="h-6 w-1/3" />
            </div>
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-baseline">
              <Skeleton className="h-4 w-1/4 mb-1 sm:mb-0" />
              <Skeleton className="h-4 w-1/2" />
            </div>
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-baseline">
              <Skeleton className="h-4 w-1/3 mb-1 sm:mb-0" />
              <Skeleton className="h-4 w-1/3" />
            </div>
          </CardContent>
          <CardFooter className="border-t pt-4 justify-end">
            <Skeleton className="h-8 w-24" />
          </CardFooter>
        </Card>
      ))}
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <CreditCard className="h-8 w-8 text-primary" />
          <h1 className="text-3xl font-bold text-primary">Payment Management</h1>
        </div>
        <Button variant="outline" onClick={fetchAllPayments} disabled={isLoading}>
          <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>
      {isLoading ? renderSkeletonCards() : payments.length === 0 ? (
        <Card className="shadow-md">
          <CardContent className="text-center py-12">
            <CreditCard className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-xl font-semibold text-primary mb-2">No Payments Found</h3>
            <p className="text-muted-foreground">There are currently no payments recorded in the system.</p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {payments.map((payment) => (
            <Card key={payment.id} className="shadow-md flex flex-col">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg font-semibold text-primary">
                      Payment ID: <span className="font-mono text-base">{payment.id}</span>
                    </CardTitle>
                    <CardDescription className="text-sm">
                      Order ID: <span className="font-mono text-xs">{payment.orderId}</span>
                    </CardDescription>
                  </div>
                  <Badge variant={getStatusBadgeVariant(payment.status)} className="capitalize text-xs px-2 py-1">
                    {payment.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-2 text-sm flex-grow">
                <div className="flex flex-col text-left sm:flex-row sm:justify-between sm:items-baseline">
                  <span className="text-sm text-muted-foreground">User ID:</span>
                  <span className="text-sm font-medium break-words font-mono text-xs">{payment.userId}</span>
                </div>
                <div className="flex flex-col text-left sm:flex-row sm:justify-between sm:items-baseline">
                  <span className="text-sm text-muted-foreground">Date:</span>
                  <span className="text-sm font-medium break-words">{new Date(payment.paymentDate).toLocaleString()}</span>
                </div>
                <div className="flex flex-col text-left sm:flex-row sm:justify-between sm:items-baseline">
                  <span className="text-sm text-muted-foreground">Amount:</span>
                  <span className="text-sm font-bold text-xl text-accent break-words">₹{payment.amount.toFixed(2)}</span>
                </div>
                <div className="flex flex-col text-left sm:flex-row sm:justify-between sm:items-baseline">
                  <span className="text-sm text-muted-foreground">Method:</span>
                  <span className="text-sm font-medium break-words">{payment.method}</span>
                </div>
                <div className="flex flex-col text-left sm:flex-row sm:justify-between sm:items-baseline">
                  <span className="text-sm text-muted-foreground">Transaction ID:</span>
                  <span className="text-sm font-medium break-words font-mono text-xs truncate max-w-[150px] sm:max-w-xs" title={payment.transactionId || 'N/A'}>
                    {payment.transactionId || 'N/A'}
                  </span>
                </div>
              </CardContent>
              <CardFooter className="border-t pt-4 justify-end">
                {payment.status === 'pending' ? (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm" disabled={isProcessingPayment === payment.id}>
                        {isProcessingPayment === payment.id ? <Loader2 className="mr-2 h-3 w-3 animate-spin" /> : <MoreVertical className="mr-1 h-4 w-4" />}
                        Actions
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleUpdatePaymentAndOrder(payment, 'completed')} disabled={isProcessingPayment === payment.id}>
                        <CheckCircle className="mr-2 h-4 w-4 text-green-600" /> Mark as Completed
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleUpdatePaymentAndOrder(payment, 'failed')} disabled={isProcessingPayment === payment.id}>
                        <XCircle className="mr-2 h-4 w-4 text-red-600" /> Mark as Failed
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                ) : (
                  <span className="text-xs text-muted-foreground italic">No actions available</span>
                )}
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}

