"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { useEffect, useState } from "react";
import { Separator } from "@/components/ui/separator";
import { Save, ShieldAlert, Lock, QrCode as QrCodeIcon, CheckCircle, Loader2 } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import Image from "next/image";
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Des<PERSON>, Di<PERSON>Footer, <PERSON><PERSON><PERSON>ead<PERSON>, DialogTitle } from "@/components/ui/dialog";
import { Label as ShadcnLabel } from "@/components/ui/label"; 


const profileFormSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }).max(50, { message: "Name cannot exceed 50 characters."}),
  email: z.string().email({ message: "Invalid email address." }),
});

const passwordFormSchema = z.object({
  currentPassword: z.string().min(1, { message: "Current password is required." }),
  newPassword: z.string().min(8, { message: "New password must be at least 8 characters." }),
  confirmNewPassword: z.string(),
}).refine(data => data.newPassword === data.confirmNewPassword, {
  message: "New passwords don't match",
  path: ["confirmNewPassword"],
});

type ProfileFormValues = z.infer<typeof profileFormSchema>;

const mockRecoveryCodes = [
  "abcd-1234-efgh-5678",
  "ijkl-9012-mnop-3456",
  "qrst-7890-uvwx-1234",
];

export default function SettingsPage() {
  const { user, updateUser, isLoadingAuth } = useAuth();
  const { toast } = useToast();
  const [is2FAEnabledState, setIs2FAEnabledState] = useState(user?.is2FAEnabled || false);
  const [show2FASetup, setShow2FASetup] = useState(false);
  const [isProfileSubmitting, setIsProfileSubmitting] = useState(false);
  const [isPasswordSubmitting, setIsPasswordSubmitting] = useState(false);

  useEffect(() => {
    if (user) {
      setIs2FAEnabledState(user.is2FAEnabled || false);
    }
  }, [user]);

  const profileForm = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      name: "",
      email: "",
    },
  });

  const passwordForm = useForm<z.infer<typeof passwordFormSchema>>({
    resolver: zodResolver(passwordFormSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmNewPassword: "",
    },
  });

  useEffect(() => {
    if (user) {
      profileForm.reset({
        name: user.name || "",
        email: user.email,
      });
    }
  }, [user, profileForm]);

  async function onProfileSubmit(values: ProfileFormValues) {
    if (!user) return;
    setIsProfileSubmitting(true);
    profileForm.clearErrors();

    const result = await updateUser({ name: values.name, email: values.email }); 
    
    if (result && result.success === false && result.fieldErrors) {
      Object.entries(result.fieldErrors).forEach(([field, messages]) => {
        if (messages && messages.length > 0) {
          profileForm.setError(field as keyof ProfileFormValues, { type: 'server', message: messages[0] });
        }
      });
      toast({ title: "Profile Update Failed", description: result.error || "Please check the fields for errors.", variant: "error" });
    } else if (result && result.success === false) {
      toast({ title: "Profile Update Failed", description: result.error || "Could not save your profile.", variant: "error" });
    } else if (result && result.success === true) {
      toast({ title: "Profile Updated", description: "Your profile information has been saved.", variant: "success" });
    }
    setIsProfileSubmitting(false);
  }

  function onPasswordSubmit(values: z.infer<typeof passwordFormSchema>) {
    setIsPasswordSubmitting(true);
    console.log("Password change attempt:", values);
    // This is a simulation. In a real app, you'd call a server action to verify the current password and update it.
    // For this prototype, we'll just show a success message if new passwords match.
    if (values.newPassword !== values.currentPassword) { // Simple check for demo
        toast({ title: "Password Change Simulated", description: "In a real app, your password would be changed.", variant: "success" });
        passwordForm.reset();
    } else {
        toast({ title: "Password Change Failed", description: "New password cannot be the same as the current password (simulated).", variant: "error" });
    }
    setIsPasswordSubmitting(false);
  }

  const handle2FAToggle = async (enabled: boolean) => {
    if (enabled) {
      setShow2FASetup(true);
    } else {
      const result = await updateUser({ is2FAEnabled: false });
      if(result && result.success){
        setIs2FAEnabledState(false);
        toast({ title: "Two-Factor Authentication Disabled", variant: "info" });
      } else {
        toast({ title: "Failed to Disable 2FA", description: result?.error || "Could not update 2FA status.", variant: "error" });
      }
    }
  };

  const confirm2FASetup = async () => {
    const result = await updateUser({ is2FAEnabled: true });
    if(result && result.success){
      setIs2FAEnabledState(true);
      setShow2FASetup(false);
      toast({ title: "Two-Factor Authentication Enabled!", description: "Make sure to save your recovery codes.", variant: "success" });
    } else {
      toast({ title: "Failed to Enable 2FA", description: result?.error || "Could not update 2FA status.", variant: "error" });
    }
  };


  if (isLoadingAuth || !user) {
    return (
        <div className="flex flex-col items-center justify-center min-h-[calc(100vh-200px)]">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="mt-2 text-lg text-muted-foreground">Loading settings...</p>
        </div>
    );
  }

  return (
    <div className="space-y-8">
      <h1 className="text-3xl font-bold text-primary">Account Settings</h1>

      <Card className="shadow-md">
        <CardHeader>
          <CardTitle className="text-xl text-primary">Profile Information</CardTitle>
          <CardDescription>Update your personal details.</CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...profileForm}>
            <form onSubmit={profileForm.handleSubmit(onProfileSubmit)} className="space-y-6">
              <FormField
                control={profileForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Full Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Your full name" {...field} disabled={isProfileSubmitting} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={profileForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email Address</FormLabel>
                    <FormControl>
                      <Input type="email" placeholder="<EMAIL>" {...field} disabled={isProfileSubmitting} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button type="submit" disabled={isProfileSubmitting}>
                {isProfileSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />} 
                {isProfileSubmitting ? "Saving..." : "Save Profile"}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>

      <Separator />

      <Card className="shadow-md">
        <CardHeader>
          <CardTitle className="text-xl text-primary">Change Password</CardTitle>
          <CardDescription>Update your account password.</CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...passwordForm}>
            <form onSubmit={passwordForm.handleSubmit(onPasswordSubmit)} className="space-y-6">
              <FormField
                control={passwordForm.control}
                name="currentPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Current Password</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder="••••••••" {...field} disabled={isPasswordSubmitting} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={passwordForm.control}
                name="newPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>New Password</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder="••••••••" {...field} disabled={isPasswordSubmitting} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={passwordForm.control}
                name="confirmNewPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Confirm New Password</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder="••••••••" {...field} disabled={isPasswordSubmitting} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button type="submit" variant="outline" disabled={isPasswordSubmitting}>
                 {isPasswordSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <ShieldAlert className="mr-2 h-4 w-4" />}
                 {isPasswordSubmitting ? "Changing..." : "Change Password"}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>

      <Separator />

      <Card className="shadow-md">
        <CardHeader>
          <CardTitle className="text-xl text-primary">Two-Factor Authentication (2FA)</CardTitle>
          <CardDescription>Enhance your account security by enabling 2FA.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="2fa-toggle"
              checked={is2FAEnabledState}
              onCheckedChange={handle2FAToggle}
              aria-label="Toggle Two-Factor Authentication"
              disabled={isProfileSubmitting} // Disable while profile is submitting as it calls updateUser
            />
            <ShadcnLabel htmlFor="2fa-toggle" className="text-base">
              {is2FAEnabledState ? "2FA is Enabled" : "2FA is Disabled"}
            </ShadcnLabel>
          </div>
          {is2FAEnabledState && (
            <Alert variant="default" className="border-green-500 bg-green-50">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <AlertTitle className="text-green-700">2FA Active</AlertTitle>
              <AlertDescription className="text-green-600">
                Your account is protected with Two-Factor Authentication.
              </AlertDescription>
            </Alert>
          )}

          <Dialog open={show2FASetup} onOpenChange={setShow2FASetup}>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle className="text-primary">Set Up Two-Factor Authentication</DialogTitle>
                <DialogDescription>
                  Scan the QR code with your authenticator app (e.g., Google Authenticator, Authy).
                </DialogDescription>
              </DialogHeader>
              <div className="py-4 space-y-4">
                <div className="flex justify-center">
                  <Image 
                    src="https://placehold.co/150x150.png?text=2FA+QR+Code" 
                    alt="Mock QR Code" 
                    width={150} 
                    height={150}
                    data-ai-hint="qrcode" 
                    className="border rounded-md"
                  />
                </div>
                <Alert>
                  <QrCodeIcon className="h-4 w-4" />
                  <AlertTitle>Authenticator App</AlertTitle>
                  <AlertDescription>
                    If you cannot scan the QR code, you would typically enter a setup key manually. (This is a simulation)
                  </AlertDescription>
                </Alert>
                <div>
                  <h4 className="font-semibold text-primary mb-2">Save Your Recovery Codes</h4>
                  <p className="text-sm text-muted-foreground mb-2">
                    Store these codes in a safe place. They can be used to access your account if you lose access to your 2FA device.
                  </p>
                  <div className="p-3 bg-muted rounded-md space-y-1">
                    {mockRecoveryCodes.map(code => (
                      <p key={code} className="font-mono text-sm text-foreground">{code}</p>
                    ))}
                  </div>
                </div>
                <Alert variant="destructive">
                  <ShieldAlert className="h-4 w-4" />
                  <AlertTitle>Important!</AlertTitle>
                  <AlertDescription>
                    This is a **simulation**. Do not use these codes or QR for a real account.
                  </AlertDescription>
                </Alert>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setShow2FASetup(false)}>Cancel</Button>
                <Button onClick={confirm2FASetup}>
                  <Lock className="mr-2 h-4 w-4" /> Done, 2FA is Set Up
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

        </CardContent>
      </Card>

    </div>
  );
}
