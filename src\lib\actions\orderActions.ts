'use server';

import type { Order } from '@/types';
import { getAllVpsPlans, getVpsPlanById } from '@/lib/actions/vpsPlanActions';
import { logActivity } from '@/lib/actions/activityLogActions';
import { readJsonFile, writeJsonFile } from '@/lib/jsonUtils';

const ORDERS_FILE = 'orders.json';

export async function createOrder(userId: string, planId: string, userEmail: string | undefined): Promise<{ order?: Order; error?: string }> {
  if (!userId || !planId) {
    return { error: "User ID and Plan ID are required to create an order." };
  }
  try {
    const plan = await getVpsPlanById(planId);
    if (!plan) {
      return { error: `Plan with ID ${planId} not found.` };
    }

    const orders = await readJsonFile<Order>(ORDERS_FILE);
    const now = new Date().toISOString();
    const newOrderId = `ORD${Date.now()}${Math.floor(Math.random() * 1000)}`;

    const newOrder: Order = {
      id: newOrderId,
      userId: userId,
      userEmail: userEmail,
      planId: planId,
      vpsPlanName: plan.name,
      orderDate: now,
      status: 'pending',
      totalAmount: plan.pricePerMonth,
      paymentMethod: 'UPI',
      vpsStatus: 'provisioning',
      operatingSystem: undefined,
    };

    orders.push(newOrder);
    await writeJsonFile<Order>(ORDERS_FILE, orders);

    await logActivity(
      userId,
      'ORDER_PLACED',
      `Order ${newOrder.id} for ${newOrder.vpsPlanName} placed.`,
      'ShoppingBag'
    );

    return { order: newOrder };

  } catch (error) {
    console.error("Error creating order:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to create order.";
    return { error: errorMessage };
  }
}

export async function getUserOrders(userId: string): Promise<Order[]> {
  if (!userId) {
    console.warn('getUserOrders called without a userId.');
    return [];
  }
  try {
    const [allOrders, vpsPlans] = await Promise.all([
      readJsonFile<Order>(ORDERS_FILE),
      getAllVpsPlans()
    ]);

    const userOrders = allOrders.filter(order => order.userId === userId);

    return userOrders.map(order => {
      const planDetails = vpsPlans.find(p => p.id === order.planId);
      return {
        ...order,
        vpsPlanName: planDetails ? planDetails.name : order.planId,
      };
    }).sort((a, b) => new Date(b.orderDate).getTime() - new Date(a.orderDate).getTime());
  } catch (error) {
    console.error(`Error fetching orders for user ${userId}:`, error);
    return [];
  }
}

export async function updateOrderStatusInJson(orderId: string, newStatus: Order['status']): Promise<{ success: boolean, error?: string, order?: Order }> {
  try {
    const orders = await readJsonFile<Order>(ORDERS_FILE);
    const orderIndex = orders.findIndex(o => o.id === orderId);
    if (orderIndex === -1) {
      return { success: false, error: "Order not found." };
    }
    orders[orderIndex].status = newStatus;

    if (newStatus === 'active' && (!orders[orderIndex].vpsStatus || orders[orderIndex].vpsStatus === 'provisioning')) {
      orders[orderIndex].vpsStatus = 'running';
      if (!orders[orderIndex].operatingSystem) orders[orderIndex].operatingSystem = "Ubuntu 22.04 LTS";
      if (!orders[orderIndex].ipAddress) orders[orderIndex].ipAddress = `192.168.1.${100 + orderIndex}`;
    } else if (newStatus === 'cancelled' || newStatus === 'fraud_review') {
      orders[orderIndex].vpsStatus = 'suspended';
    } else if (newStatus === 'processing' && (orders[orderIndex].status === 'pending' || orders[orderIndex].status === 'fraud_review')) {
      orders[orderIndex].vpsStatus = 'provisioning';
    }

    await writeJsonFile<Order>(ORDERS_FILE, orders);
    console.log(`Order ${orderId} status updated to ${newStatus}.`);
    return { success: true, order: orders[orderIndex] };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : "Failed to update order status.";
    console.error("Error updating order status:", error);
    return { success: false, error: errorMessage };
  }
}

export async function countUserOrdersByStatus(userId: string, status: Order['status']): Promise<number> {
  if (!userId) {
    return 0;
  }
  try {
    const allOrders = await readJsonFile<Order>(ORDERS_FILE);
    return allOrders.filter(order => order.userId === userId && order.status === status).length;
  } catch (error) {
    console.error(`Error counting orders for user ${userId} with status ${status}:`, error);
    return 0;
  }
}

export async function updateUserVpsStatus(
  orderId: string,
  newVpsStatus: Order['vpsStatus'],
  userId: string
): Promise<{ success: boolean; error?: string; order?: Order }> {
  try {
    const orders = await readJsonFile<Order>(ORDERS_FILE);
    const orderIndex = orders.findIndex(o => o.id === orderId);

    if (orderIndex === -1) {
      return { success: false, error: 'Order not found.' };
    }

    if (orders[orderIndex].userId !== userId) {
      return { success: false, error: 'User not authorized to modify this VPS.' };
    }

    if (orders[orderIndex].status !== 'active' && newVpsStatus !== 'suspended') {
      return { success: false, error: 'VPS actions only allowed for active or suspended services.' };
    }
    if (orders[orderIndex].vpsStatus === 'suspended' && newVpsStatus !== 'stopped' && newVpsStatus !== 'running' && newVpsStatus !== 'suspended') {
      // Allowing unsuspending to stopped or running
    }

    const oldVpsStatus = orders[orderIndex].vpsStatus;
    orders[orderIndex].vpsStatus = newVpsStatus;

    if (newVpsStatus === 'rebooting') {
      setTimeout(async () => {
        const currentOrders = await readJsonFile<Order>(ORDERS_FILE);
        const currentOrderIndex = currentOrders.findIndex(o => o.id === orderId);
        if (currentOrderIndex !== -1 && currentOrders[currentOrderIndex].vpsStatus === 'rebooting') {
          currentOrders[currentOrderIndex].vpsStatus = 'running';
          await writeJsonFile<Order>(ORDERS_FILE, currentOrders);
          console.log(`VPS ${orderId} rebooted and set to running.`);
          await logActivity(
            userId,
            'VPS_STARTED',
            `VPS for order ${orderId} finished rebooting and is now running.`,
            'Play'
          );
        }
      }, 3000);
    }

    await writeJsonFile<Order>(ORDERS_FILE, orders);

    if (newVpsStatus !== 'rebooting' && oldVpsStatus !== newVpsStatus) {
      let activityType: 'VPS_STARTED' | 'VPS_STOPPED' | undefined = undefined;
      let activityIcon: 'Play' | 'PowerOff' | undefined = undefined;

      if (newVpsStatus === 'running') {
        activityType = 'VPS_STARTED';
        activityIcon = 'Play';
      } else if (newVpsStatus === 'stopped' || newVpsStatus === 'suspended') {
        activityType = 'VPS_STOPPED';
        activityIcon = 'PowerOff';
      }

      if (activityType) {
        await logActivity(
          userId,
          activityType,
          `VPS for order ${orderId} status changed to ${newVpsStatus}.`,
          activityIcon
        );
      }
    }

    return { success: true, order: orders[orderIndex] };
  } catch (error) {
    console.error('Error updating VPS status:', error);
    return { success: false, error: 'Failed to update VPS status.' };
  }
}
