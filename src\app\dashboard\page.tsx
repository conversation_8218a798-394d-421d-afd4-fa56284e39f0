"use client";
import { <PERSON>, CardContent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button, buttonVariants } from "@/components/ui/button";
import { ShoppingBag, CreditCard, ArrowRight, Loader2, Bell, UserCog, Ticket, Play, LogIn, Activity as ActivityIcon, RefreshCw } from "lucide-react";
import Link from "next/link";
import { useAuth } from "@/hooks/useAuth";
import { useEffect, useState, useCallback } from "react";
import { countUserOrdersByStatus } from "@/lib/actions/orderActions";
import { countUserPaymentsByStatus } from "@/lib/actions/paymentActions";
import { countUserTicketsByStatus } from "@/lib/actions/ticketActions";
import type { ActivityLogEntry } from "@/types";
import { getUserActivity } from "@/lib/actions/activityLogActions";
import { formatDistanceToNowStrict } from 'date-fns';
import { cn } from "@/lib/utils";

interface DashboardStats {
  activeServices: number;
  pendingInvoices: number;
  openTickets: number;
}

// Moved PowerOff definition before iconMap
const PowerOff = ({ className }: { className?: string }) => <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}><path d="M18.36 6.64a9 9 0 1 1-12.73 0"></path><line x1="12" y1="2" x2="12" y2="12"></line></svg>;

// Helper to dynamically get Lucide icons, provide default if not found
const LucideIcons: { [key: string]: React.ElementType } = {
  ShoppingBag,
  CreditCard,
  Ticket,
  Play,
  PowerOff, // Custom component used here
  UserCog,
  LogIn,
  Bell,
  ActivityIcon,
};


export default function DashboardPage() {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [isLoadingStats, setIsLoadingStats] = useState(true);
  const [recentActivities, setRecentActivities] = useState<ActivityLogEntry[]>([]);
  const [isLoadingActivities, setIsLoadingActivities] = useState(true);


  const fetchDashboardData = useCallback(async () => {
    if (user?.id) {
      setIsLoadingStats(true);
      setIsLoadingActivities(true);
      try {
        const [activeServices, pendingInvoices, openTickets, activities] = await Promise.all([
          countUserOrdersByStatus(user.id, 'active'),
          countUserPaymentsByStatus(user.id, 'pending'),
          countUserTicketsByStatus(user.id, ['open', 'in_progress']),
          getUserActivity(user.id, 5)
        ]);
        setStats({ activeServices, pendingInvoices, openTickets });
        setRecentActivities(activities);
      } catch (error) {
        console.error("Failed to fetch dashboard data:", error);
        setStats({ activeServices: 0, pendingInvoices: 0, openTickets: 0 }); // Fallback
        setRecentActivities([]);
      } finally {
        setIsLoadingStats(false);
        setIsLoadingActivities(false);
      }
    } else {
      setIsLoadingStats(false);
      setIsLoadingActivities(false);
      setStats(null);
      setRecentActivities([]);
    }
  }, [user?.id]);


  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);


  const statCardsData = [
    {
      title: "Active Services",
      valueKey: "activeServices" as keyof DashboardStats,
      description: "Manage your running VPS instances.",
      link: "/dashboard/orders",
      icon: ShoppingBag,
    },
    {
      title: "Pending Payments",
      valueKey: "pendingInvoices" as keyof DashboardStats,
      description: "You have outstanding payments.",
      link: "/dashboard/payment-history",
      icon: CreditCard,
    },
    {
      title: "Open Tickets",
      valueKey: "openTickets" as keyof DashboardStats,
      description: "Active support requests.",
      link: "/support",
      icon: Ticket,
    }
  ];


  if (isLoadingStats && user) {
    return (
      <div className="space-y-8">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-primary">Welcome, {user?.name || 'User'}!</h1>
            <p className="text-muted-foreground">Loading your account overview...</p>
          </div>
          <a
            href="/vps-offerings"
            className={cn(
              buttonVariants({ size: "lg", variant: "default" }),
              "shadow-md hover:shadow-lg hover:scale-105 transition-transform duration-200 ease-in-out flex items-center opacity-50 cursor-not-allowed"
            )}
          >
            <span>Order New Service</span> <ArrowRight className="ml-2 h-4 w-4" />
          </a>
        </div>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {statCardsData.map((card, index) => (
            <Card key={index} className="shadow-sm hover:shadow-md transition-shadow flex flex-col">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-primary">{card.title}</CardTitle>
                <card.icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent className="flex-grow">
                <div className="text-2xl font-bold text-primary"><Loader2 className="h-6 w-6 animate-spin" /></div>
                <p className="text-xs text-muted-foreground h-8">
                  {card.description}
                </p>
              </CardContent>
              <CardFooter className="p-4 border-t mt-auto">
                <a
                  href={card.link}
                  className={cn(buttonVariants({ variant: "ghost", size: "sm" }), "text-accent flex items-center justify-between w-full opacity-50 cursor-not-allowed")}
                >
                  <span>Loading...</span>
                  <ArrowRight className="h-4 w-4" />
                </a>
              </CardFooter>
            </Card>
          ))}
        </div>
        <Card className="shadow-sm">
          <CardHeader className="flex flex-row justify-between items-center">
            <div>
              <CardTitle className="text-xl text-primary">Recent Account Activity</CardTitle>
              <CardDescription>Overview of recent events on your account.</CardDescription>
            </div>
            <Button variant="outline" size="icon" disabled>
              <RefreshCw className="h-4 w-4 animate-spin" />
            </Button>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-md">
              <Loader2 className="h-5 w-5 animate-spin text-primary" />
              <p className="text-sm text-foreground">Loading activity...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-primary">Welcome, {user?.name || 'User'}!</h1>
          <p className="text-muted-foreground">Here's an overview of your SkyHosting account.</p>
        </div>
        <Link href="/vps-offerings" legacyBehavior passHref>
          <a
            className={cn(
              buttonVariants({ size: "lg", variant: "default" }),
              "shadow-md hover:shadow-lg hover:scale-105 transition-transform duration-200 ease-in-out flex items-center"
            )}
          >
            <span>Order New Service</span> <ArrowRight className="ml-2 h-4 w-4" />
          </a>
        </Link>
      </div>
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {statCardsData.map((card, index) => (
          <Card key={index} className="shadow-md hover:shadow-lg hover:border-primary/50 transition-all duration-200 ease-in-out flex flex-col">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-primary">{card.title}</CardTitle>
              <card.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent className="flex-grow">
              <div className="text-2xl font-bold text-primary">{stats ? stats[card.valueKey] : <Loader2 className="h-6 w-6 animate-spin" />}</div>
              <p className="text-xs text-muted-foreground h-8">
                {card.description}
              </p>
            </CardContent>
            <CardFooter className="p-4 border-t mt-auto">
              <Link href={card.link} passHref legacyBehavior>
                <a className={cn(buttonVariants({ variant: "ghost", size: "sm" }), "text-accent flex items-center justify-between w-full")}>
                  <span>View {card.title.split(' ')[0]}</span>
                  <ArrowRight className="h-4 w-4" />
                </a>
              </Link>
            </CardFooter>
          </Card>
        ))}
      </div>
      <Card className="shadow-sm">
        <CardHeader className="flex flex-row justify-between items-center">
          <div>
            <CardTitle className="text-xl text-primary">Recent Account Activity</CardTitle>
            <CardDescription>Overview of recent events on your account.</CardDescription>
          </div>
          <Button variant="outline" size="icon" onClick={fetchDashboardData} disabled={isLoadingActivities}>
            <RefreshCw className={cn("h-4 w-4", isLoadingActivities && "animate-spin")} />
          </Button>
        </CardHeader>
        <CardContent>
          {isLoadingActivities ? (
            <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-md">
              <Loader2 className="h-5 w-5 animate-spin text-primary" />
              <p className="text-sm text-foreground">Loading activity...</p>
            </div>
          ) : recentActivities.length === 0 ? (
            <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-md">
              <ActivityIcon className="h-5 w-5 text-muted-foreground" />
              <p className="text-sm text-muted-foreground">No recent activity to display.</p>
            </div>
          ) : (
            <ul className="space-y-3">
              {recentActivities.map((activity, index) => { // Added index here
                const IconComponent = activity.icon ? (LucideIcons[activity.icon as keyof typeof LucideIcons] || ActivityIcon) : ActivityIcon;
                return (
                  <li
                    key={activity.id}
                    className={cn(
                      "flex items-center space-x-3 p-3 bg-muted/50 rounded-md hover:bg-muted/75 transition-colors",
                      "animate-in fade-in-0 slide-in-from-bottom-2 duration-300 ease-out" // Animation classes
                    )}
                    style={{ animationDelay: `${index * 75}ms` }} // Staggered delay
                  >
                    <IconComponent className="h-5 w-5 text-primary flex-shrink-0" />
                    <div className="flex-grow">
                      <p className="text-sm text-foreground">{activity.description}</p>
                      <p className="text-xs text-muted-foreground">
                        {formatDistanceToNowStrict(new Date(activity.timestamp), { addSuffix: true })}
                      </p>
                    </div>
                  </li>
                );
              })}
            </ul>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
