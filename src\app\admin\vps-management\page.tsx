"use client";

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import type { Order } from "@/types";
import { Server, MoreVertical, Loader2, Power, Play, RotateCcw, Ban, ShieldCheck, RefreshCw } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from '@/hooks/use-toast';
import { adminGetAllVpsInstances, adminUpdateVpsStatus } from '@/lib/actions/adminActions';
import { Skeleton } from '@/components/ui/skeleton';

export default function AdminVpsManagementPage() {
  const { toast } = useToast();
  const [vpsInstances, setVpsInstances] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [updatingVpsId, setUpdatingVpsId] = useState<string | null>(null);

  const fetchVpsInstances = async () => {
    setIsLoading(true);
    try {
      const fetchedInstances = await adminGetAllVpsInstances();
      setVpsInstances(fetchedInstances);
    } catch (error) {
      console.error("Failed to fetch VPS instances:", error);
      toast({
        title: "Error",
        description: "Could not load VPS instance data.",
        variant: "error",
      });
      setVpsInstances([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchVpsInstances();
  }, []);

  const getOrderStatusVariant = (status: Order['status']) => {
    switch (status) {
      case 'active': return 'default';
      case 'pending': return 'secondary';
      case 'cancelled': return 'destructive';
      case 'fraud_review': return 'outline';
      case 'processing': return 'secondary';
      default: return 'outline';
    }
  };

  const getVpsStatusVariant = (status?: Order['vpsStatus']) => {
    switch (status) {
      case 'running': return 'default';
      case 'stopped': return 'destructive';
      case 'rebooting': return 'secondary';
      case 'suspended': return 'destructive';
      case 'provisioning': return 'secondary';
      case 'error': return 'destructive';
      default: return 'outline';
    }
  };

  const handleAdminVpsAction = async (orderId: string, action: Order['vpsStatus']) => {
    if (!action) return;
    setUpdatingVpsId(orderId);
    const result = await adminUpdateVpsStatus(orderId, action);
    setUpdatingVpsId(null);

    if (result.success && result.order) {
      setVpsInstances(prevInstances => prevInstances.map(inst => inst.id === orderId ? { ...inst, ...result.order } : inst));
      toast({ title: "VPS Action Success", description: `VPS ${orderId} is now ${action}.`, variant: "success" });
      if (action === 'rebooting') {
        setTimeout(() => fetchVpsInstances(), 4000); // Re-fetch after simulated reboot
      }
    } else {
      toast({ title: "VPS Action Failed", description: result.error || "Could not perform action.", variant: "error" });
    }
  };

  const renderSkeletonCards = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {[...Array(4)].map((_, i) => (
        <Card key={i} className="shadow-md flex flex-col">
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <Skeleton className="h-5 w-3/4 mb-1" />
                <Skeleton className="h-3 w-1/2" />
              </div>
              <Skeleton className="h-8 w-8 rounded-sm" />
            </div>
          </CardHeader>
          <CardContent className="space-y-3 text-sm flex-grow">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-baseline">
              <Skeleton className="h-4 w-1/4 mb-1 sm:mb-0" />
              <Skeleton className="h-4 w-1/2" />
            </div>
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-baseline">
              <Skeleton className="h-4 w-1/5 mb-1 sm:mb-0" />
              <Skeleton className="h-4 w-2/3" />
            </div>
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-baseline">
              <Skeleton className="h-4 w-1/3 mb-1 sm:mb-0" />
              <Skeleton className="h-4 w-1/3" />
            </div>
          </CardContent>
          <CardFooter className="border-t pt-4 flex flex-col items-start gap-3 sm:flex-row sm:justify-between sm:items-center sm:gap-4">
            <Skeleton className="h-6 w-24" />
            <Skeleton className="h-6 w-20" />
          </CardFooter>
        </Card>
      ))}
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Server className="h-8 w-8 text-primary" />
          <h1 className="text-3xl font-bold text-primary">VPS Instance Management</h1>
        </div>
        <Button variant="outline" onClick={fetchVpsInstances} disabled={isLoading}>
          <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {isLoading ? renderSkeletonCards() : vpsInstances.length === 0 ? (
        <Card className="shadow-md">
          <CardContent className="text-center py-12">
            <Server className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-xl font-semibold text-primary mb-2">No VPS Instances Found</h3>
            <p className="text-muted-foreground">There are currently no VPS instances recorded in the system.</p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {vpsInstances.map((instance) => (
            <Card key={instance.id} className="shadow-md flex flex-col">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg font-semibold text-primary">
                      Order ID: <span className="font-mono text-base">{instance.id}</span>
                    </CardTitle>
                    <CardDescription className="text-xs">
                      User: {instance.userEmail || instance.userId}
                    </CardDescription>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreVertical className="h-4 w-4" />
                        {updatingVpsId === instance.id && <Loader2 className="absolute h-4 w-4 animate-spin" />}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {instance.vpsStatus === 'running' && (
                        <DropdownMenuItem onClick={() => handleAdminVpsAction(instance.id, 'stopped')} disabled={updatingVpsId === instance.id}>
                          <Power className="mr-2 h-4 w-4" /> Stop Server
                        </DropdownMenuItem>
                      )}
                      {instance.vpsStatus === 'stopped' && (
                        <DropdownMenuItem onClick={() => handleAdminVpsAction(instance.id, 'running')} disabled={updatingVpsId === instance.id}>
                          <Play className="mr-2 h-4 w-4" /> Start Server
                        </DropdownMenuItem>
                      )}
                      {(instance.vpsStatus === 'running' || instance.vpsStatus === 'stopped') && (
                        <DropdownMenuItem onClick={() => handleAdminVpsAction(instance.id, 'rebooting')} disabled={updatingVpsId === instance.id}>
                          <RotateCcw className="mr-2 h-4 w-4" /> Reboot Server
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuSeparator />
                      {instance.vpsStatus !== 'suspended' ? (
                        <DropdownMenuItem onClick={() => handleAdminVpsAction(instance.id, 'suspended')} disabled={updatingVpsId === instance.id}>
                          <Ban className="mr-2 h-4 w-4" /> Suspend VPS
                        </DropdownMenuItem>
                      ) : (
                        <DropdownMenuItem onClick={() => handleAdminVpsAction(instance.id, 'stopped')} disabled={updatingVpsId === instance.id}>
                          <ShieldCheck className="mr-2 h-4 w-4" /> Unsuspend VPS
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent className="space-y-2 text-sm flex-grow">
                <div className="flex flex-col text-left sm:flex-row sm:justify-between sm:items-baseline">
                  <span className="text-sm text-muted-foreground">Plan:</span>
                  <span className="text-sm font-medium break-words">{instance.vpsPlanName || instance.planId}</span>
                </div>
                <div className="flex flex-col text-left sm:flex-row sm:justify-between sm:items-baseline">
                  <span className="text-sm text-muted-foreground">IP Address:</span>
                  <span className="text-sm font-medium break-words font-mono text-xs">{instance.ipAddress || 'N/A'}</span>
                </div>
                <div className="flex flex-col text-left sm:flex-row sm:justify-between sm:items-baseline">
                  <span className="text-sm text-muted-foreground">Operating System:</span>
                  <span className="text-sm font-medium break-words">{instance.operatingSystem || 'N/A'}</span>
                </div>
              </CardContent>
              <CardFooter className="border-t pt-4 flex flex-col items-start gap-3 sm:flex-row sm:justify-between sm:items-center sm:gap-4">
                <div>
                  <span className="text-xs text-muted-foreground mr-1">Order Status: </span>
                  <Badge variant={getOrderStatusVariant(instance.status)} className="capitalize text-xs">
                    {instance.status.replace('_', ' ')}
                  </Badge>
                </div>
                <div>
                  <span className="text-xs text-muted-foreground mr-1">VPS Status: </span>
                  {instance.vpsStatus ? (
                    <Badge variant={getVpsStatusVariant(instance.vpsStatus)} className="capitalize text-xs">
                      {instance.vpsStatus.replace('_', ' ')}
                      {updatingVpsId === instance.id && <Loader2 className="ml-1 h-3 w-3 animate-spin" />}
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="text-xs">N/A</Badge>
                  )}
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}

