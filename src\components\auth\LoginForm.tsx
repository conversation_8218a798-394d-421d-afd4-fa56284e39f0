"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import { LogIn, KeyRound } from "lucide-react";
import { useState } from "react";
import type { User } from "@/types";
import { loginUser } from "@/lib/actions/authActions";
import { DemoLoginRow } from "@/components/DemoLoginRow";

const loginSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address." }),
  password: z.string().min(1, { message: "Password cannot be empty." }),
});

const twoFactorSchema = z.object({
  twoFactorCode: z.string().length(6, { message: "2FA code must be 6 digits." }).regex(/^\d+$/, "Must be digits"),
});

type LoginFormValues = z.infer<typeof loginSchema>;
type TwoFactorFormValues = z.infer<typeof twoFactorSchema>;

export function LoginForm() {
  const { login: contextLogin, isLoadingAuth } = useAuth();
  const { toast } = useToast();
  const router = useRouter();
  const [loginStep, setLoginStep] = useState<'credentials' | '2fa'>('credentials');
  const [currentUserFor2FA, setCurrentUserFor2FA] = useState<User | null>(null);
  const [currentSessionIdFor2FA, setCurrentSessionIdFor2FA] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const showDemoSection = true;

  const loginForm = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const twoFactorForm = useForm<TwoFactorFormValues>({
    resolver: zodResolver(twoFactorSchema),
    defaultValues: {
      twoFactorCode: "",
    },
  });

  async function onCredentialSubmit(values: LoginFormValues) {
    setIsProcessing(true);
    loginForm.clearErrors(); // Clear previous errors
    const result = await loginUser(values.email, values.password);
    setIsProcessing(false);

    if (result.success === false) {
      if (result.fieldErrors) {
        (Object.keys(result.fieldErrors) as Array<keyof LoginFormValues>).forEach((field) => {
          const messages = result.fieldErrors?.[field];
          if (messages && messages.length > 0) {
            loginForm.setError(field, { type: 'server', message: messages[0] });
          }
        });
      }
      toast({ title: "Login Failed", description: result.error || "An unknown error occurred.", variant: "error" });
    } else if (result.user && result.sessionId) {
      const loggedInUser = result.user;
      if (loggedInUser.is2FAEnabled) {
        setCurrentUserFor2FA(loggedInUser);
        setCurrentSessionIdFor2FA(result.sessionId);
        setLoginStep('2fa');
        toast({ title: "Credentials Verified", description: "Please enter your 2FA code.", variant: "info" });
      } else {
        contextLogin(result.sessionId, loggedInUser);
        toast({ title: "Login Successful", description: "Welcome back!", variant: "success" });
        router.push(loggedInUser.role === "admin" ? "/admin" : "/dashboard");
      }
    } else {
      // Fallback for unexpected successful result structure
      toast({ title: "Login Failed", description: "An unexpected error occurred during login.", variant: "error" });
    }
  }

  function onTwoFactorSubmit(values: TwoFactorFormValues) {
    setIsProcessing(true);
    if (currentUserFor2FA && currentSessionIdFor2FA && values.twoFactorCode.length === 6 && /^\d{6}$/.test(values.twoFactorCode)) {
      contextLogin(currentSessionIdFor2FA, currentUserFor2FA);
      toast({ title: "Login Successful", description: "Welcome back! 2FA Verified.", variant: "success" });
      router.push(currentUserFor2FA.role === "admin" ? "/admin" : "/dashboard");
    } else {
      toast({ title: "2FA Failed", description: "Invalid 2FA code. Please try again.", variant: "error" });
    }
    setIsProcessing(false);
  }

  const isLoading = isProcessing || isLoadingAuth;

  return (
    <Card className="w-full max-w-md mx-auto shadow-xl">
      <CardHeader className="text-center">
        <CardTitle className="text-3xl font-bold text-primary">
          {loginStep === 'credentials' ? 'Welcome Back!' : 'Two-Factor Authentication'}
        </CardTitle>
        <CardDescription>
          {loginStep === 'credentials'
            ? 'Sign in to access your SkyHosting account.'
            : `Enter the code from your authenticator app for ${currentUserFor2FA?.email}.`}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {loginStep === 'credentials' && (
          <Form {...loginForm}>
            <form onSubmit={loginForm.handleSubmit(onCredentialSubmit)} className="space-y-6">
              <FormField
                control={loginForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email Address</FormLabel>
                    <FormControl>
                      <Input placeholder="<EMAIL>" {...field} disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={loginForm.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder="••••••••" {...field} disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button type="submit" className="w-full" size="lg" disabled={isLoading}>
                {isLoading ? "Signing In..." : <><LogIn className="mr-2 h-5 w-5" /> Sign In</>}
              </Button>
            </form>
          </Form>
        )}

        {loginStep === '2fa' && (
          <Form {...twoFactorForm}>
            <form onSubmit={twoFactorForm.handleSubmit(onTwoFactorSubmit)} className="space-y-6">
              <FormField
                control={twoFactorForm.control}
                name="twoFactorCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Authentication Code</FormLabel>
                    <FormControl>
                      <Input placeholder="123456" {...field} maxLength={6} autoFocus disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button type="submit" className="w-full" size="lg" disabled={isLoading}>
                {isLoading ? "Verifying..." : <><KeyRound className="mr-2 h-5 w-5" /> Verify Code</>}
              </Button>
              <Button variant="link" onClick={() => { setLoginStep('credentials'); setCurrentUserFor2FA(null); setCurrentSessionIdFor2FA(null); }} className="w-full" disabled={isLoading}>
                Back to login
              </Button>
            </form>
          </Form>
        )}
      </CardContent>
      {loginStep === 'credentials' && (
        <CardFooter className="flex flex-col items-center space-y-2">
          <Link href="/auth/forgot-password">
            <Button variant="link" className="text-sm text-muted-foreground">
              Forgot your password?
            </Button>
          </Link>
          <p className="text-sm text-muted-foreground">
            Don't have an account?{" "}
            <Link href="/signup" className="font-medium text-accent hover:underline">
              Sign up
            </Link>
          </p>

          {/* Conditional Demo Login Section */}
          {showDemoSection && (
            <div className="demo-login-section w-full mt-4 space-y-4 text-center">
              {/* Divider */}
              <hr className="border-t border-gray-200 w-full" />
              <h4 className="text-sm font-semibold uppercase text-gray-500 tracking-wide">
                Demo User Login
              </h4>
              <DemoLoginRow email="<EMAIL>" password="password" />
              <DemoLoginRow email="<EMAIL>" password="password" />
              <hr className="border-gray-200 my-3" />
              <h4 className="text-sm font-semibold uppercase text-gray-500 tracking-wide">
                Demo Admin Login
              </h4>
              <DemoLoginRow email="<EMAIL>" password="adminpass" />
              <p className="text-[10px] text-gray-400 mt-1 italic">
                Hover to show buttons — click to copy email or password
              </p>
            </div>
          )}
        </CardFooter>
      )}
    </Card>
  );
}
