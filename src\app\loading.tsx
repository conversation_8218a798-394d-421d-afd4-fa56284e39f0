import { Skeleton } from "@/components/ui/skeleton";

export default function HomePageLoading() {
  return (
    <div className="space-y-16">
      {/* Hero Slideshow Skeleton */}
      <section>
        <Skeleton className="w-full aspect-[16/7] md:aspect-[16/6] lg:aspect-[16/5] rounded-lg" />
      </section>

      {/* Features Overview Skeleton */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <Skeleton className="h-10 w-3/4 md:w-1/2 mx-auto mb-12" />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
                <Skeleton className="w-12 h-12 rounded-full mb-4" />
                <Skeleton className="h-6 w-3/4 mb-2" />
                <Skeleton className="h-4 w-full mb-1" />
                <Skeleton className="h-4 w-5/6" />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action Skeleton */}
      <section className="py-16 bg-muted/30 rounded-lg shadow-lg">
        <div className="container mx-auto px-4 text-center">
          <Skeleton className="h-10 w-3/4 md:w-1/2 mx-auto mb-6" />
          <Skeleton className="h-5 w-full md:w-3/4 mx-auto mb-8" />
          <Skeleton className="h-12 w-40 mx-auto" />
        </div>
      </section>

      {/* Our Top VPS Plans Skeleton */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <Skeleton className="h-10 w-3/4 md:w-1/2 mx-auto mb-12" />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="rounded-lg border bg-card text-card-foreground shadow-sm flex flex-col h-full">
                <div className="p-6 pt-8">
                  <Skeleton className="h-8 w-3/4 mb-2" />
                  <Skeleton className="h-4 w-full mb-1" />
                  <Skeleton className="h-4 w-5/6 mb-6" />
                  <Skeleton className="h-10 w-1/2 mb-6" />
                  <div className="space-y-3">
                    {[...Array(4)].map((_, j) => (
                      <div key={j} className="flex items-center">
                        <Skeleton className="w-5 h-5 mr-3 rounded-full" />
                        <Skeleton className="h-4 w-3/4" />
                      </div>
                    ))}
                  </div>
                  <Skeleton className="h-5 w-1/3 mt-6 mb-2" />
                  <div className="space-y-2">
                    {[...Array(2)].map((_, k) => (
                      <div key={k} className="flex items-center">
                        <Skeleton className="w-4 h-4 mr-2 rounded-full" />
                        <Skeleton className="h-4 w-full" />
                      </div>
                    ))}
                  </div>
                </div>
                <div className="p-6 mt-auto bg-muted/30">
                  <Skeleton className="h-12 w-full" />
                </div>
              </div>
            ))}
          </div>
          <div className="text-center mt-12">
            <Skeleton className="h-10 w-32 mx-auto" />
          </div>
        </div>
      </section>
    </div>
  );
}
