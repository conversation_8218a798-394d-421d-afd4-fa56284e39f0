"use client";

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from "@/hooks/useAuth";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import type { SupportTicket, TicketStatus, TicketPriority } from "@/types";
import { LifeBuoy, Ticket as TicketIconLucide, PlusCircle, List, Send, Loader2, AlignLeft, Tag, Flag, CalendarDays, ChevronRight, ChevronLeft } from "lucide-react";
import { getUserTickets, createTicket, getTicketCategories, getTicketPriorities, type TicketActionResult } from '@/lib/actions/ticketActions';
import { cn } from '@/lib/utils';
import { Label as ShadcnLabel } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';

const newTicketFormSchema = z.object({
  subject: z.string().min(5, "Subject must be at least 5 characters.").max(100, "Subject cannot exceed 100 characters."),
  category: z.string({ required_error: "Please select a category." }).min(1, "Please select a category."),
  description: z.string().min(20, "Description must be at least 20 characters.").max(2000, "Description cannot exceed 2000 characters."),
});

type NewTicketFormData = z.infer<typeof newTicketFormSchema>;


const ITEMS_PER_PAGE_USER = 4;

export default function SupportPage() {
  const { isAuthenticated, user, isLoadingAuth } = useAuth(); // Added isLoadingAuth
  const router = useRouter();
  const { toast } = useToast();
  const [userTickets, setUserTickets] = useState<SupportTicket[]>([]);
  const [isLoadingTickets, setIsLoadingTickets] = useState(true);
  const [isSubmittingTicket, setIsSubmittingTicket] = useState(false);
  const [ticketCategories, setTicketCategories] = useState<string[]>([]);
  const [ticketPriorities, setTicketPriorities] = useState<{ value: TicketPriority, label: string }[]>([]);
  const [isLoadingOptions, setIsLoadingOptions] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);

  const form = useForm<NewTicketFormData>({
    resolver: zodResolver(newTicketFormSchema),
    defaultValues: {
      subject: "",
      category: "",
      description: "",
    },
  });

  const fetchTicketOptions = useCallback(async () => {
    setIsLoadingOptions(true);
    try {
      const [categories, priorities] = await Promise.all([
        getTicketCategories(),
        getTicketPriorities()
      ]);
      setTicketCategories(categories);
      setTicketPriorities(priorities);
    } catch (error) {
      toast({
        title: "Error fetching ticket options",
        description: error instanceof Error ? error.message : "Could not load form options.",
        variant: "error",
      });
    } finally {
      setIsLoadingOptions(false);
    }
  }, [toast]);

  const fetchTickets = useCallback(async () => {
    if (!user?.id) {
      setIsLoadingTickets(false);
      return;
    }
    setIsLoadingTickets(true);
    try {
      const tickets = await getUserTickets(user.id);
      setUserTickets(tickets);
    } catch (error) {
      toast({
        title: "Error fetching tickets",
        description: error instanceof Error ? error.message : "Could not load your support tickets.",
        variant: "error",
      });
      setUserTickets([]);
    } finally {
      setIsLoadingTickets(false);
    }
  }, [user, toast]);

  useEffect(() => {
    fetchTicketOptions();
  }, [fetchTicketOptions]);

  useEffect(() => {
    if (!isLoadingAuth) { // Only run after auth state is determined
      if (isAuthenticated === false) {
        router.push('/login?redirect=/support');
      } else if (isAuthenticated === true && user) {
        fetchTickets();
      }
    }
  }, [isAuthenticated, user, router, fetchTickets, isLoadingAuth]);


  async function onSubmitNewTicket(values: NewTicketFormData) {
    if (!user?.id) {
      toast({ title: "Error", description: "User not identified. Cannot create ticket.", variant: "error" });
      return;
    }
    setIsSubmittingTicket(true);
    form.clearErrors();

    const result: TicketActionResult = await createTicket({
      userId: user.id,
      subject: values.subject,
      category: values.category,
      description: values.description,
    });
    setIsSubmittingTicket(false);

    if (!result.success) {
      if (result.fieldErrors) {
        Object.entries(result.fieldErrors).forEach(([field, messages]) => {
          if (messages && Array.isArray(messages) && messages.length > 0) {
            form.setError(field as keyof NewTicketFormData, { type: 'server', message: messages[0] });
          }
        });
        toast({
          title: "Ticket Submission Failed",
          description: result.error || "Please check the form for errors.",
          variant: "error",
        });
      } else {
        toast({
          title: "Ticket Submission Failed",
          description: result.error || "An unknown error occurred.",
          variant: "error",
        });
      }
    } else {
      toast({
        title: "Ticket Submitted Successfully!",
        description: `Your support ticket "${values.subject}" has been created. Ticket ID: ${result.ticket?.id}`,
        variant: "success",
      });
      form.reset();
      fetchTickets();
      setCurrentPage(1);
    }
  }

  const getStatusBadgeVariant = (status: TicketStatus) => {
    switch (status) {
      case 'open': return 'default';
      case 'in_progress': return 'secondary';
      case 'resolved': return 'outline';
      case 'closed': return 'destructive';
      default: return 'outline';
    }
  };

  const getPriorityBadgeVariant = (priority: TicketPriority) => {
    switch (priority) {
      case 'low': return 'secondary';
      case 'medium': return 'default';
      case 'high': return 'outline';
      case 'urgent': return 'destructive';
      default: return 'outline';
    }
  };

  const getStatusBorderClass = (status?: TicketStatus): string => {
    switch (status) {
      case 'open': return 'border-primary border-2 shadow-md hover:shadow-lg';
      case 'in_progress': return 'border-accent border-2 shadow-md hover:shadow-lg';
      case 'resolved': return 'border-green-500 border-2 shadow-md hover:shadow-lg';
      case 'closed': return 'border-muted-foreground border-2 opacity-80 shadow-sm hover:shadow-md';
      default: return 'border-border shadow-sm hover:shadow-md';
    }
  };

  const totalPages = Math.ceil(userTickets.length / ITEMS_PER_PAGE_USER);
  const paginatedTickets = userTickets.slice(
    (currentPage - 1) * ITEMS_PER_PAGE_USER,
    currentPage * ITEMS_PER_PAGE_USER
  );

  const renderMyTicketsSkeleton = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {[...Array(ITEMS_PER_PAGE_USER)].map((_, i) => (
        <Card key={i} className="flex flex-col relative shadow-sm border-border">
          <Skeleton className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 h-6 w-20 rounded-full" />
          <CardHeader className="pt-8">
            <Skeleton className="h-5 w-3/4 mb-1" />
          </CardHeader>
          <CardContent className="space-y-4 text-sm flex-grow pt-4">
            {[1, 2, 3].map((j) => (
              <div key={j} className="mt-2">
                <Skeleton className="h-4 w-1/4 mb-1" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            ))}
          </CardContent>
          <CardFooter className="border-t pt-4 flex justify-between items-center">
            <Skeleton className="h-6 w-24" />
          </CardFooter>
        </Card>
      ))}
    </div>
  );


  if (isLoadingAuth) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-200px)]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="ml-2 text-lg text-muted-foreground">Loading support center...</p>
      </div>
    );
  }

  if (isAuthenticated === false) { // Explicit check after isLoadingAuth is false
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-200px)]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="ml-2 text-lg text-muted-foreground my-4">Redirecting to login...</p>
        <Button asChild className="mt-4"><Link href="/login?redirect=/support">Go to Login</Link></Button>
      </div>
    );
  }

  if (!user || isLoadingOptions) { // If authenticated but user or options still loading
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-200px)]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="ml-2 text-lg text-muted-foreground">Loading user data & options...</p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="flex items-center space-x-3">
        <LifeBuoy className="h-10 w-10 text-primary" />
        <div>
          <h1 className="text-3xl font-bold text-primary">Support Center</h1>
          <p className="text-muted-foreground">Get help with your services or report issues.</p>
        </div>
      </div>
      <Tabs defaultValue="my-tickets" className="w-full">
        <TabsList className="grid w-full grid-cols-2 md:w-[400px]">
          <TabsTrigger value="my-tickets"><List className="mr-2 h-4 w-4" /> My Tickets</TabsTrigger>
          <TabsTrigger value="create-ticket"><PlusCircle className="mr-2 h-4 w-4" /> Create New Ticket</TabsTrigger>
        </TabsList>
        <TabsContent value="my-tickets">
          <Card className="shadow-md mt-4">
            <CardHeader>
              <CardTitle>Your Support Tickets</CardTitle>
              <CardDescription>View the status of your submitted support requests.</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingTickets ? (
                renderMyTicketsSkeleton()
              ) : userTickets.length === 0 ? (
                <div className="text-center py-12">
                  <TicketIconLucide className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-xl font-semibold text-primary mb-2">No Support Tickets Yet</h3>
                  <p className="text-muted-foreground">
                    You haven't created any support tickets. If you need help, please use the "Create New Ticket" tab.
                  </p>
                </div>
              ) : (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {paginatedTickets.map((ticket) => (
                      <Card
                        key={ticket.id}
                        className={cn(
                          "flex flex-col relative transition-shadow duration-200",
                          getStatusBorderClass(ticket.status)
                        )}
                      >
                        <Badge
                          variant={getPriorityBadgeVariant(ticket.priority)}
                          className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 px-3 py-1 text-xs font-semibold rounded-full shadow-lg z-10 capitalize"
                        >
                          {ticketPriorities.find(p => p.value === ticket.priority)?.label || ticket.priority}
                        </Badge>
                        <CardHeader className="pt-8">
                          <CardTitle className="text-lg font-semibold text-primary">
                            Ticket ID: <span className="font-mono text-base">{ticket.id}</span>
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4 text-sm flex-grow pt-4">
                          <div>
                            <ShadcnLabel className="flex items-center">
                              <AlignLeft className="mr-1.5 h-4 w-4 text-muted-foreground" /> Subject
                            </ShadcnLabel>
                            <p className="font-semibold text-base truncate text-primary mt-1" title={ticket.subject}>
                              {ticket.subject}
                            </p>
                          </div>
                          <div>
                            <ShadcnLabel className="flex items-center">
                              <Tag className="mr-1.5 h-4 w-4 text-muted-foreground" /> Category
                            </ShadcnLabel>
                            <p className="font-medium text-primary mt-1">{ticket.category}</p>
                          </div>
                          <div>
                            <ShadcnLabel className="flex items-center">
                              <Flag className="mr-1.5 h-4 w-4 text-muted-foreground" /> Priority
                            </ShadcnLabel>
                            <p className="font-medium text-primary mt-1 capitalize">
                              {ticketPriorities.find(p => p.value === ticket.priority)?.label || ticket.priority}
                            </p>
                          </div>
                          <div>
                            <ShadcnLabel className="flex items-center">
                              <CalendarDays className="mr-1.5 h-4 w-4 text-muted-foreground" /> Last Updated
                            </ShadcnLabel>
                            <p className="font-medium text-primary mt-1">{new Date(ticket.updatedAt).toLocaleString()}</p>
                          </div>
                        </CardContent>
                        <CardFooter className="border-t pt-4 flex justify-between items-center">
                          <div>
                            <span className="text-xs text-muted-foreground">Status: </span>
                            <Badge variant={getStatusBadgeVariant(ticket.status)} className="capitalize text-xs px-2 py-1">
                              {ticket.status.replace('_', ' ')}
                            </Badge>
                          </div>
                        </CardFooter>
                      </Card>
                    ))}
                  </div>
                  {totalPages > 1 && (
                    <div className="flex items-center justify-center space-x-4 pt-6">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                        disabled={currentPage === 1}
                      >
                        <ChevronLeft className="mr-2 h-4 w-4" /> Previous
                      </Button>
                      <span className="text-sm text-muted-foreground">
                        Page {currentPage} of {totalPages}
                      </span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                        disabled={currentPage === totalPages}
                      >
                        Next <ChevronRight className="ml-2 h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="create-ticket">
          <Card className="shadow-md mt-4">
            <CardHeader>
              <CardTitle>Create a New Support Ticket</CardTitle>
              <CardDescription>Describe your issue or question, and our team will get back to you.</CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmitNewTicket)} className="space-y-6">
                  <FormField
                    control={form.control}
                    name="subject"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Subject</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., Unable to access my server" {...field} disabled={isSubmittingTicket || isLoadingOptions} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="category"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Category</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isSubmittingTicket || isLoadingOptions || ticketCategories.length === 0}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a category" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {ticketCategories.map(cat => (
                              <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Please provide as much detail as possible about your issue or question..."
                            className="min-h-[150px]"
                            {...field}
                            disabled={isSubmittingTicket || isLoadingOptions}
                          />
                        </FormControl>
                        <FormDescription>
                          Include any relevant error messages, steps to reproduce, or specific details.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <Button type="submit" size="lg" disabled={isSubmittingTicket || isLoadingOptions}>
                    {isSubmittingTicket ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Send className="mr-2 h-4 w-4" />}
                    {isSubmittingTicket ? 'Submitting...' : 'Submit Ticket'}
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

