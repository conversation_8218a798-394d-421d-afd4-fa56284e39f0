import type { VPSPlan } from '@/types';
import { <PERSON>, CardContent, CardDescription, Card<PERSON>oot<PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { CheckCircle, Cpu, MemoryStick, HardDrive, Zap } from 'lucide-react';
import Link from 'next/link';
import { Badge } from "@/components/ui/badge";
import { cn } from '@/lib/utils';

interface VpsOfferingCardProps {
  plan: VPSPlan;
  buttonText?: string;
  buttonHref?: string;
}

const getSpecialTagBgColor = (tag?: VPSPlan['specialTag']) => {
  switch (tag) {
    case 'Sale':
      return 'bg-destructive text-destructive-foreground';
    case 'Most Bought':
      return 'bg-primary text-primary-foreground';
    case 'Best Price':
      return 'bg-green-600 text-white';
    case 'New':
      return 'bg-blue-500 text-white';
    case 'Recommended':
      return 'bg-accent text-accent-foreground';
    default:
      return 'bg-primary text-primary-foreground';
  }
};

const getSpecialTagBorderClass = (tag?: VPSPlan['specialTag']) => {
  if (!tag) return '';

  switch (tag) {
    case 'Sale':
      return 'border-2 border-destructive';
    case 'Most Bought':
      return 'border-2 border-primary';
    case 'Best Price':
      return 'border-2 border-green-600';
    case 'New':
      return 'border-2 border-blue-500';
    case 'Recommended':
      return 'border-2 border-accent';
    default:
      return '';
  }
};

export function VpsOfferingCard({ plan, buttonText, buttonHref }: VpsOfferingCardProps) {
  return (
    <Card className={cn(
      "relative flex flex-col h-full shadow-lg hover:shadow-xl transition-shadow duration-300 rounded-lg", // overflow-hidden removed
      getSpecialTagBorderClass(plan.specialTag)
    )}>
      {plan.specialTag && (
        <div
          className={`absolute top-0 -left-3 -mt-3 z-10 text-base font-semibold px-2 py-1 rounded-md shadow-md ${getSpecialTagBgColor(plan.specialTag)}`}
        >
          {plan.specialTag}
        </div>
      )}
      {plan.discountLabel && plan.originalPricePerMonth && plan.pricePerMonth < plan.originalPricePerMonth && (
        <div className="absolute top-0 -right-3 -mt-3 z-10 shadow-md">
          <Badge variant="destructive" className="text-base px-2 py-1 rounded-md hover:bg-red-500 hover:text-white hover:opacity-100 hover:shadow-none">
            {plan.discountLabel}
          </Badge>
        </div>
      )}
      <CardHeader className="p-6 pt-8">
        <CardTitle className="text-2xl font-semibold text-primary">{plan.name}</CardTitle>
        <CardDescription className="text-sm text-muted-foreground min-h-[40px]">{plan.description}</CardDescription>
      </CardHeader>
      <CardContent className="p-6 flex-grow">
        <div className="mb-6">
          {plan.originalPricePerMonth && plan.pricePerMonth < plan.originalPricePerMonth ? (
            <div className="flex flex-row items-baseline flex-wrap gap-x-2 gap-y-1">
              <span className="text-lg font-normal text-muted-foreground line-through">
                ₹{plan.originalPricePerMonth}
              </span>
              <p className="text-4xl font-bold text-accent leading-none">
                ₹{plan.pricePerMonth}
                <span className="text-lg font-normal text-muted-foreground">/mo</span>
              </p>
            </div>
          ) : (
            <p className="text-4xl font-bold text-accent leading-none">
              ₹{plan.pricePerMonth}
              <span className="text-lg font-normal text-muted-foreground">/mo</span>
            </p>
          )}
        </div>
        <ul className="space-y-3 text-sm text-foreground">
          <li className="flex items-center">
            <Cpu className="w-5 h-5 mr-3 text-accent" />
            <span>{plan.cpuCores} CPU Core{plan.cpuCores > 1 ? 's' : ''}</span>
          </li>
          <li className="flex items-center">
            <MemoryStick className="w-5 h-5 mr-3 text-accent" />
            <span>{plan.ramGB} GB RAM</span>
          </li>
          <li className="flex items-center">
            <HardDrive className="w-5 h-5 mr-3 text-accent" />
            <span>{plan.storageGB} GB SSD Storage</span>
          </li>
          <li className="flex items-center">
            <Zap className="w-5 h-5 mr-3 text-accent" />
            <span>{plan.bandwidthTB} TB Bandwidth</span>
          </li>
        </ul>
        <h4 className="font-semibold mt-6 mb-2 text-primary">Key Features:</h4>
        <ul className="space-y-2 text-sm text-muted-foreground">
          {plan.features.map((feature, index) => (
            <li key={index} className="flex items-center">
              <CheckCircle className="w-4 h-4 mr-2 text-green-500 flex-shrink-0" />
              <span>{feature}</span>
            </li>
          ))}
        </ul>
      </CardContent>
      <CardFooter className="p-6 bg-muted/30">
        <Button className="w-full" size="lg" asChild>
          <Link href={buttonHref || `/payment/upi?plan=${plan.id}`}>
            {buttonText || 'Order Now'}
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
}
