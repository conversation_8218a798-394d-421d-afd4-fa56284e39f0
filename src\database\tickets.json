[{"id": "TICKET_INIT_001", "userId": "user001", "subject": "Initial Test Ticket from JSON", "description": "This is a test support ticket loaded from the tickets.json file to demonstrate persistence. I'm having an issue with my first server.", "category": "Technical Issue", "priority": "medium", "status": "open", "createdAt": "2024-05-20T10:00:00.000Z", "updatedAt": "2024-05-20T10:00:00.000Z"}, {"id": "TICKET_INIT_002", "userId": "user002", "subject": "Billing question for 2FA User", "description": "I have a question regarding my last invoice and how 2FA setup affects it, if at all.", "category": "Billing Inquiry", "priority": "low", "status": "open", "createdAt": "2024-05-21T14:30:00.000Z", "updatedAt": "2024-05-21T14:30:00.000Z"}, {"id": "TICKET_HIGH_003", "userId": "user001", "subject": "Urgent: Server Down, Website Inaccessible!", "description": "My main production server (ORD103 - Business VPS) is completely down. None of my websites are accessible. This is critical!", "category": "Technical Issue", "priority": "urgent", "status": "closed", "createdAt": "2024-05-22T08:15:00.000Z", "updatedAt": "2025-05-21T13:51:47.991Z", "resolvedAt": "2025-05-21T13:51:47.991Z"}, {"id": "TICKET_PERF_004", "userId": "user002", "subject": "Slow performance on Developer VPS", "description": "My Developer VPS (ORD101) has been extremely sluggish for the past few hours. Basic commands are taking a long time to execute.", "category": "Performance Issue", "priority": "high", "status": "in_progress", "createdAt": "2024-05-22T10:00:00.000Z", "updatedAt": "2024-05-22T11:30:00.000Z"}, {"id": "TICKET_FEAT_005", "userId": "admin001", "subject": "Feature Request: Add API access for VPS management", "description": "It would be great if we could have an API to manage VPS instances programmatically (start, stop, reboot).", "category": "Feature Request", "priority": "low", "status": "resolved", "createdAt": "2024-05-19T16:00:00.000Z", "updatedAt": "2024-05-21T10:00:00.000Z", "resolvedAt": "2024-05-21T10:00:00.000Z"}, {"id": "TICKET_BILL_006", "userId": "user001", "subject": "Invoice discrepancy for May", "description": "I believe my invoice for May is incorrect. It shows a charge for a service I cancelled last month.", "category": "Billing Inquiry", "priority": "medium", "status": "open", "createdAt": "2024-05-23T09:00:00.000Z", "updatedAt": "2024-05-23T09:00:00.000Z"}, {"id": "TICKET_GEN_007", "userId": "user002", "subject": "General question about SSH keys", "description": "How do I add multiple SSH public keys to my VPS server for different users?", "category": "General Question", "priority": "low", "status": "closed", "createdAt": "2024-05-18T11:00:00.000Z", "updatedAt": "2024-05-20T15:00:00.000Z", "resolvedAt": "2024-05-20T14:00:00.000Z"}, {"id": "TICKET_SEC_008", "userId": "admin001", "subject": "Suspicious login attempts detected", "description": "We've noticed multiple failed login attempts from an unknown IP on server xyz. Please investigate.", "category": "Technical Issue", "priority": "high", "status": "in_progress", "createdAt": "2024-05-23T14:20:00.000Z", "updatedAt": "2024-05-23T14:50:00.000Z"}, {"id": "TICKET_UPG_009", "userId": "user001", "subject": "Request to upgrade my Starter VPS", "description": "I'd like to upgrade my current Starter VPS plan to the Developer VPS plan. What's the process?", "category": "Billing Inquiry", "priority": "medium", "status": "open", "createdAt": "2024-05-24T07:30:00.000Z", "updatedAt": "2024-05-24T07:30:00.000Z"}, {"id": "TICKET_BUG_010", "userId": "user002", "subject": "Bug Report: Payment history page not loading correctly", "description": "The payment history page in my dashboard is showing a blank screen sometimes. I'm using Chrome.", "category": "Technical Issue", "priority": "medium", "status": "resolved", "createdAt": "2024-05-17T10:00:00.000Z", "updatedAt": "2024-05-19T12:00:00.000Z", "resolvedAt": "2024-05-19T12:00:00.000Z"}, {"id": "TICKET_NET_011", "userId": "user001", "subject": "Network connectivity issues to specific region", "description": "I'm experiencing high latency and packet loss when connecting to services hosted in the EU region from my VPS.", "category": "Performance Issue", "priority": "high", "status": "open", "createdAt": "2024-05-24T11:00:00.000Z", "updatedAt": "2024-05-24T11:00:00.000Z"}, {"id": "TICKET_PWD_012", "userId": "user002", "subject": "Password reset not working", "description": "I tried resetting my VPS root password from the control panel, but I'm still unable to log in with the new password.", "category": "Technical Issue", "priority": "urgent", "status": "in_progress", "createdAt": "2024-05-24T13:45:00.000Z", "updatedAt": "2024-05-24T14:00:00.000Z"}, {"id": "TICKET_INFO_013", "userId": "admin001", "subject": "Info: Scheduled Maintenance for Database Servers", "description": "This is to inform that database servers will undergo scheduled maintenance on May 30th, 2 AM UTC. Expect brief interruptions.", "category": "General Question", "priority": "low", "status": "closed", "createdAt": "2024-05-15T09:00:00.000Z", "updatedAt": "2024-05-15T09:00:00.000Z", "resolvedAt": "2024-05-15T09:00:00.000Z"}, {"id": "TICKET_COMP_014", "userId": "user001", "subject": "<PERSON><PERSON><PERSON>t about support response time", "description": "I submitted a ticket (TICKET_HIGH_003) marked urgent, and it's been 4 hours without a substantial update. This is not acceptable.", "category": "General Question", "priority": "high", "status": "open", "createdAt": "2024-05-22T12:15:00.000Z", "updatedAt": "2024-05-22T12:15:00.000Z"}, {"id": "TICKET_BKUP_015", "userId": "user002", "subject": "How to restore from backup?", "description": "I accidentally deleted some files on my Developer VPS. How can I initiate a restore from the weekly backups?", "category": "Technical Issue", "priority": "medium", "status": "open", "createdAt": "2024-05-25T10:30:00.000Z", "updatedAt": "2024-05-25T10:30:00.000Z"}, {"id": "TICKET_DNS_016", "userId": "user001", "subject": "Help with DNS configuration", "description": "I'm trying to point my domain to my VPS IP, but I'm not sure how to configure the A records and NS records correctly.", "category": "Technical Issue", "priority": "medium", "status": "resolved", "createdAt": "2024-05-16T14:00:00.000Z", "updatedAt": "2024-05-18T10:00:00.000Z", "resolvedAt": "2024-05-18T10:00:00.000Z"}, {"id": "TICKET_SSL_017", "userId": "user002", "subject": "Issue installing Let's Encrypt SSL", "description": "I'm following a guide to install a Let's Encrypt SSL certificate on my Ubuntu server, but Certbot is failing with an error.", "category": "Technical Issue", "priority": "high", "status": "in_progress", "createdAt": "2024-05-25T15:00:00.000Z", "updatedAt": "2024-05-25T16:00:00.000Z"}, {"id": "TICKET_ABUSE_018", "userId": "admin001", "subject": "Abuse Report: Phishing site hosted on IP *************", "description": "We received an abuse report regarding a phishing website being hosted on ORD103 (user001). Immediate action required.", "category": "Technical Issue", "priority": "urgent", "status": "open", "createdAt": "2024-05-25T17:00:00.000Z", "updatedAt": "2024-05-25T17:00:00.000Z"}, {"id": "TICKET_LIMIT_019", "userId": "user001", "subject": "Question about resource limits", "description": "What happens if my VPS exceeds its allocated bandwidth or CPU limits? Will it be suspended automatically?", "category": "General Question", "priority": "low", "status": "open", "createdAt": "2024-05-26T10:00:00.000Z", "updatedAt": "2024-05-26T10:00:00.000Z"}, {"id": "TICKET_CLOSE_020", "userId": "user002", "subject": "Request to close my account", "description": "I no longer require SkyHosting services and would like to request the closure of my account and deletion of all my data.", "category": "Billing Inquiry", "priority": "medium", "status": "closed", "createdAt": "2024-05-14T13:00:00.000Z", "updatedAt": "2024-05-16T11:00:00.000Z", "resolvedAt": "2024-05-16T10:00:00.000Z"}]