'use server';

import type { ActivityLogEntry } from '@/types';
import { readJsonFile, writeJsonFile } from '@/lib/jsonUtils';

const ACTIVITY_LOG_FILE = 'activity-log.json';

export async function getUserActivity(userId: string, limit: number = 5): Promise<ActivityLogEntry[]> {
  if (!userId) {
    return [];
  }
  try {
    const allActivities = await readJsonFile<ActivityLogEntry>(ACTIVITY_LOG_FILE);
    const userActivities = allActivities
      .filter(activity => activity.userId === userId || activity.type === 'GENERIC_NOTIFICATION')
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit);
    return userActivities;
  } catch (error) {
    console.error(`Error fetching activity log for user ${userId}:`, error);
    return [];
  }
}

export async function logActivity(
  userId: string,
  type: ActivityLogEntry['type'],
  description: string,
  icon?: ActivityLogEntry['icon']
): Promise<{ activity?: ActivityLogEntry; error?: string }> {
  if (!userId || !type || !description) {
    return { error: "User ID, type, and description are required to log activity." };
  }
  try {
    const activities = await readJsonFile<ActivityLogEntry>(ACTIVITY_LOG_FILE);
    const newActivity: ActivityLogEntry = {
      id: `ACT${Date.now()}${Math.floor(Math.random() * 1000)}`,
      userId,
      timestamp: new Date().toISOString(),
      type,
      description,
      icon,
    };
    activities.push(newActivity);
    await writeJsonFile<ActivityLogEntry>(ACTIVITY_LOG_FILE, activities);
    return { activity: newActivity };
  } catch (error) {
    console.error("Error logging activity:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to log activity.";
    return { error: errorMessage };
  }
}
