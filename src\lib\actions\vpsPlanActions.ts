'use server';

import type { VPSPlan } from '@/types';
import { readJsonFile, writeJsonFile } from '@/lib/jsonUtils';
import * as z from "zod";

const VPS_PLANS_FILE = 'vps-plans.json';

const specialTagOptionsServer: (VPSPlan['specialTag'] | "" | undefined)[] = ["", "Most Bought", "Best Price", "New", "Recommended", "Sale"];

const planValidationSchema = z.object({
  name: z.string().min(3, { message: "Plan name must be at least 3 characters." }).max(50, { message: "Plan name cannot exceed 50 characters." }),
  description: z.string().min(10, { message: "Description must be at least 10 characters." }).max(500, { message: "Description cannot exceed 500 characters." }),
  pricePerMonth: z.coerce.number().positive({ message: "Price must be a positive number." }),
  originalPricePerMonth: z.union([z.coerce.number().positive(), z.literal('')]).optional().transform(val => val === '' ? undefined : val),
  discountLabel: z.string().max(20, { message: "Discount label cannot exceed 20 characters." }).optional().transform(val => val === '' ? undefined : val),
  specialTag: z.enum(specialTagOptionsServer as [string, ...string[]]).optional().transform(val => val === '' ? undefined : val),
  cpuCores: z.coerce.number().int({ message: "CPU Cores must be an integer." }).positive({ message: "CPU Cores must be a positive integer." }),
  ramGB: z.coerce.number().int({ message: "RAM must be an integer." }).positive({ message: "RAM must be a positive integer." }),
  storageGB: z.coerce.number().int({ message: "Storage must be an integer." }).positive({ message: "Storage must be a positive integer." }),
  bandwidthTB: z.coerce.number().positive({ message: "Bandwidth must be a positive number." }),
  features: z.string().min(1, { message: "Please list at least one feature (each on a new line)." }),
});

export interface PlanActionResult {
  success: boolean;
  plan?: VPSPlan;
  error?: string;
  fieldErrors?: Partial<Record<keyof z.infer<typeof planValidationSchema>, string[]>>;
}

export async function getAllVpsPlans(): Promise<VPSPlan[]> {
  try {
    const plans = await readJsonFile<VPSPlan>(VPS_PLANS_FILE);
    return plans.sort((a, b) => a.pricePerMonth - b.pricePerMonth);
  } catch (error) {
    console.error('Error in getAllVpsPlans:', error);
    return [];
  }
}

export async function getVpsPlanById(planId: string): Promise<VPSPlan | undefined> {
  try {
    const plans = await readJsonFile<VPSPlan>(VPS_PLANS_FILE);
    return plans.find(plan => plan.id === planId);
  } catch (error) {
    console.error(`Error fetching plan by ID ${planId}:`, error);
    return undefined;
  }
}

type ClientPlanFormData = z.infer<typeof planValidationSchema>;

export async function createVpsPlan(planDataFromClient: ClientPlanFormData): Promise<PlanActionResult> {
  const validationResult = planValidationSchema.safeParse(planDataFromClient);
  if (!validationResult.success) {
    return {
      success: false,
      error: "Validation failed. Please check the fields below.",
      fieldErrors: validationResult.error.flatten().fieldErrors
    };
  }

  const validatedData = validationResult.data;

  try {
    const plans = await readJsonFile<VPSPlan>(VPS_PLANS_FILE);
    const newPlanId = (validatedData.name).toLowerCase().replace(/\s+/g, '-') + '-' + Date.now().toString(36).slice(-4);

    if (plans.some(p => p.id === newPlanId)) {
      return { success: false, error: 'A plan with the generated ID already exists. Please try a slightly different name.' };
    }
    if (plans.some(p => p.name.toLowerCase() === validatedData.name.toLowerCase())) {
      return {
        success: false,
        error: "Validation failed.",
        fieldErrors: { name: ["A plan with this name already exists."] }
      };
    }

    const newPlan: VPSPlan = {
      id: newPlanId,
      name: validatedData.name,
      description: validatedData.description,
      pricePerMonth: validatedData.pricePerMonth,
      originalPricePerMonth: validatedData.originalPricePerMonth,
      discountLabel: validatedData.discountLabel,
      specialTag: validatedData.specialTag as VPSPlan['specialTag'],
      cpuCores: validatedData.cpuCores,
      ramGB: validatedData.ramGB,
      storageGB: validatedData.storageGB,
      bandwidthTB: validatedData.bandwidthTB,
      features: validatedData.features.split('\\n').map(f => f.trim()).filter(f => f.length > 0),
    };
    plans.push(newPlan);
    await writeJsonFile<VPSPlan>(VPS_PLANS_FILE, plans);
    return { success: true, plan: newPlan };
  } catch (error) {
    console.error('Error creating VPS plan:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Failed to create VPS plan.' };
  }
}

export async function updateVpsPlan(planId: string, updatesFromClient: Partial<ClientPlanFormData>): Promise<PlanActionResult> {
  try {
    let plans = await readJsonFile<VPSPlan>(VPS_PLANS_FILE);
    const planIndex = plans.findIndex(p => p.id === planId);
    if (planIndex === -1) {
      return { success: false, error: 'Plan not found.' };
    }

    const currentPlan = plans[planIndex];

    const dataToValidate = {
      name: updatesFromClient.name ?? currentPlan.name,
      description: updatesFromClient.description ?? currentPlan.description,
      pricePerMonth: updatesFromClient.pricePerMonth ?? currentPlan.pricePerMonth,
      originalPricePerMonth: (typeof updatesFromClient.originalPricePerMonth === 'string' && updatesFromClient.originalPricePerMonth === '') ? undefined : (updatesFromClient.originalPricePerMonth ?? currentPlan.originalPricePerMonth),
      discountLabel: updatesFromClient.discountLabel === '' ? undefined : (updatesFromClient.discountLabel ?? currentPlan.discountLabel),
      specialTag: updatesFromClient.specialTag === '' ? undefined : (updatesFromClient.specialTag ?? currentPlan.specialTag),
      cpuCores: updatesFromClient.cpuCores ?? currentPlan.cpuCores,
      ramGB: updatesFromClient.ramGB ?? currentPlan.ramGB,
      storageGB: updatesFromClient.storageGB ?? currentPlan.storageGB,
      bandwidthTB: updatesFromClient.bandwidthTB ?? currentPlan.bandwidthTB,
      features: typeof updatesFromClient.features === 'string' ? updatesFromClient.features : currentPlan.features.join('\\n'),
    };

    const validationResult = planValidationSchema.safeParse(dataToValidate);
    if (!validationResult.success) {
      return {
        success: false,
        error: "Validation failed. Please check the fields below.",
        fieldErrors: validationResult.error.flatten().fieldErrors
      };
    }

    const validatedData = validationResult.data;

    if (validatedData.name.toLowerCase() !== currentPlan.name.toLowerCase() && plans.some(p => p.name.toLowerCase() === validatedData.name.toLowerCase() && p.id !== planId)) {
      return {
        success: false,
        error: "Validation failed.",
        fieldErrors: { name: ["Another plan with this name already exists."] }
      };
    }

    const updatedPlan: VPSPlan = {
      ...currentPlan,
      name: validatedData.name,
      description: validatedData.description,
      pricePerMonth: validatedData.pricePerMonth,
      originalPricePerMonth: validatedData.originalPricePerMonth,
      discountLabel: validatedData.discountLabel,
      specialTag: validatedData.specialTag as VPSPlan['specialTag'],
      cpuCores: validatedData.cpuCores,
      ramGB: validatedData.ramGB,
      storageGB: validatedData.storageGB,
      bandwidthTB: validatedData.bandwidthTB,
      features: validatedData.features.split('\\n').map(f => f.trim()).filter(f => f.length > 0),
    };

    plans[planIndex] = updatedPlan;
    await writeJsonFile<VPSPlan>(VPS_PLANS_FILE, plans);
    return { success: true, plan: updatedPlan };
  } catch (error) {
    console.error('Error updating VPS plan:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Failed to update VPS plan.' };
  }
}

export async function deleteVpsPlan(planId: string): Promise<{ success?: boolean; error?: string }> {
  try {
    let plans = await readJsonFile<VPSPlan>(VPS_PLANS_FILE);
    const initialLength = plans.length;
    // TODO: In a real app, check if this planId is used by any active orders before deleting.
    plans = plans.filter(p => p.id !== planId);
    if (plans.length === initialLength) {
      return { error: 'Plan not found for deletion.' };
    }
    await writeJsonFile<VPSPlan>(VPS_PLANS_FILE, plans);
    return { success: true };
  } catch (error) {
    console.error('Error deleting VPS plan:', error);
    return { error: error instanceof Error ? error.message : 'Failed to delete VPS plan.' };
  }
}
