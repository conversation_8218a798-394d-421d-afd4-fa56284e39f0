@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 94%; /* Off-White #f0f0f0 */
    --foreground: 210 29% 10%; /* Dark shade for text */

    --card: 0 0% 100%; /* White */
    --card-foreground: 210 29% 10%;

    --popover: 0 0% 100%;
    --popover-foreground: 210 29% 10%;

    --primary: 210 29% 24%; /* Midnight Blue #2c3e50 */
    --primary-foreground: 0 0% 98%; /* Light color for text on primary */

    --secondary: 210 30% 88%; /* Lighter, less saturated version of primary or neutral gray */
    --secondary-foreground: 210 29% 24%; /* Primary color for text on secondary */

    --muted: 210 30% 92%; /* Even lighter gray for muted backgrounds */
    --muted-foreground: 210 20% 45%; /* Softer text color */

    --accent: 180 100% 25%; /* Teal #008080 */
    --accent-foreground: 0 0% 98%; /* Light color for text on accent */

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border-destructive: 0 84.2% 50.2%; /* Darker red for border */

    --success: 145 63% 42%; 
    --success-foreground: 145 100% 98%;
    --border-success: 145 63% 35%;

    --info: 200 85% 50%; 
    --info-foreground: 200 100% 98%;
    --border-info: 200 85% 40%;

    --warning: 40 90% 50%; 
    --warning-foreground: 40 100% 10%; 
    --border-warning: 40 90% 40%;

    --border: 210 20% 80%; /* Light border */
    --input: 210 20% 85%; /* Slightly lighter for input fields */
    --ring: 180 100% 30%; /* Accent color for focus rings */

    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 210 29% 10%; 
    --foreground: 0 0% 94%; 

    --card: 210 29% 12%; 
    --card-foreground: 0 0% 94%;

    --popover: 210 29% 12%;
    --popover-foreground: 0 0% 94%;

    --primary: 0 0% 98%; 
    --primary-foreground: 210 29% 10%; 

    --secondary: 210 29% 18%; 
    --secondary-foreground: 0 0% 98%;

    --muted: 210 29% 18%;
    --muted-foreground: 0 0% 63.9%;

    --accent: 180 100% 35%; 
    --accent-foreground: 0 0% 98%;

    --destructive: 0 70% 50.6%; /* Adjusted for dark mode */
    --destructive-foreground: 0 0% 98%;
    --border-destructive: 0 70% 40.6%;

    --success: 145 50% 50%;
    --success-foreground: 145 100% 98%;
    --border-success: 145 50% 40%;

    --info: 200 70% 60%;
    --info-foreground: 200 100% 98%;
    --border-info: 200 70% 50%;

    --warning: 40 70% 55%; /* Adjusted for better visibility on dark */
    --warning-foreground: 40 100% 10%;
    --border-warning: 40 70% 45%;

    --border: 210 29% 20%;
    --input: 210 29% 20%;
    --ring: 180 100% 40%; 

    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .text-shadow-sm {
    text-shadow: 0 1px 2px rgba(0,0,0,0.25);
  }
  .text-shadow-md {
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  }
  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0,0,0,0.3);
  }
  .text-shadow-none {
    text-shadow: none;
  }
}
