import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Server, ShieldCheck, DollarSign, LifeBuoy, Globe, Layers, TerminalSquare, Gamepad2 } from "lucide-react";
import Link from "next/link";
import { VpsOfferingCard } from "@/components/vps/VpsOfferingCard";
import { HeroSlideshow, type SlideContent, type RawSlideData } from "@/components/home/<USER>";
import { getAllVpsPlans } from "@/lib/actions/vpsPlanActions";
import { getSlideshowData } from "@/lib/actions/homepageActions";
import React from 'react';

export default async function HomePage() {
  const [allPlans, rawSlidesData] = await Promise.all([
    getAllVpsPlans(),
    getSlideshowData() as Promise<RawSlideData[]> // Cast to RawSlideData array
  ]);

  const topPlans = allPlans.slice(0, 4);

  // Transform raw slide data to include JSX titles
  const slides: SlideContent[] = rawSlidesData.map(rawSlide => {
    const { titleConfig, ...restOfRawSlide } = rawSlide;
    const TitleElement = (titleConfig.element || 'h1') as keyof JSX.IntrinsicElements;

    let titleContent;
    if (titleConfig.highlight) {
      const highlightSpan = (
        <span className={titleConfig.highlight.className}>
          {titleConfig.highlight.text}
        </span>
      );
      titleContent = titleConfig.highlight.position === 'before'
        ? <>{highlightSpan} {titleConfig.text}</>
        : <>{titleConfig.text} {highlightSpan}</>;
    } else {
      titleContent = titleConfig.text;
    }

    return {
      ...restOfRawSlide,
      title: (
        <TitleElement className={titleConfig.className} style={titleConfig.style}>
          {titleContent}
        </TitleElement>
      ),
    };
  });

  const useCases = [
    {
      icon: Globe,
      title: "Host Websites & Blogs",
      description: "Launch lightning-fast websites, blogs, or e-commerce stores with reliable uptime and full control.",
    },
    {
      icon: Layers,
      title: "Deploy Applications",
      description: "Run your custom applications, APIs, and backend services with dedicated resources and your chosen stack.",
    },
    {
      icon: TerminalSquare,
      title: "Develop & Test",
      description: "Create sandboxed environments for development, testing, and staging your projects efficiently.",
    },
    {
      icon: Gamepad2,
      title: "Run Game Servers",
      description: "Host high-performance game servers for your community with low latency and complete customization.",
    },
  ];

  return (
    <div className="space-y-16">
      {/* Hero Slideshow Section */}
      <section>
        <HeroSlideshow slides={slides} />
      </section>

      {/* Features Overview Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold text-center text-primary mb-12">
            Why Choose SkyHosting?
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card className="shadow-md hover:shadow-xl hover:-translate-y-1 transition-all duration-300 ease-in-out">
              <CardHeader>
                <div className="flex items-center justify-center w-12 h-12 bg-accent/10 text-accent rounded-full mb-4">
                  <Server className="w-6 h-6" />
                </div>
                <CardTitle className="text-xl text-primary">High Performance VPS</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Blazing fast SSD storage, powerful CPUs, and ample RAM to ensure your applications run smoothly and efficiently.
                </CardDescription>
              </CardContent>
            </Card>
            <Card className="shadow-md hover:shadow-xl hover:-translate-y-1 transition-all duration-300 ease-in-out">
              <CardHeader>
                <div className="flex items-center justify-center w-12 h-12 bg-accent/10 text-accent rounded-full mb-4">
                  <ShieldCheck className="w-6 h-6" />
                </div>
                <CardTitle className="text-xl text-primary">Robust Security</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Advanced security measures including DDoS protection, firewalls, and regular backups to keep your data safe.
                </CardDescription>
              </CardContent>
            </Card>
            <Card className="shadow-md hover:shadow-xl hover:-translate-y-1 transition-all duration-300 ease-in-out">
              <CardHeader>
                <div className="flex items-center justify-center w-12 h-12 bg-accent/10 text-accent rounded-full mb-4">
                  <DollarSign className="w-6 h-6" />
                </div>
                <CardTitle className="text-xl text-primary">Transparent Pricing</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Affordable and straightforward pricing plans with no hidden fees. Choose the perfect plan for your budget and needs.
                </CardDescription>
              </CardContent>
            </Card>
            <Card className="shadow-md hover:shadow-xl hover:-translate-y-1 transition-all duration-300 ease-in-out">
              <CardHeader>
                <div className="flex items-center justify-center w-12 h-12 bg-accent/10 text-accent rounded-full mb-4">
                  <LifeBuoy className="w-6 h-6" />
                </div>
                <CardTitle className="text-xl text-primary">24/7 Expert Support</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Our dedicated support team is available around the clock to assist you with any questions or issues.
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* New "Unleash Your Potential" Section */}
      <section className="py-16 bg-muted/30 rounded-lg shadow-lg">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold text-center text-primary mb-6">
            Unleash Your Potential with SkyHosting
          </h2>
          <p className="text-lg md:text-xl text-center text-muted-foreground mb-12 max-w-2xl mx-auto">
            From personal blogs to demanding applications, our VPS platform provides the power and flexibility you need to succeed.
          </p>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {useCases.map((useCase, index) => (
              <Card key={index} className="shadow-sm hover:shadow-lg transition-shadow flex flex-col items-center text-center bg-card">
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-center w-16 h-16 bg-accent/10 text-accent rounded-full mb-4 mx-auto">
                    <useCase.icon className="w-8 h-8" />
                  </div>
                  <CardTitle className="text-lg text-primary">{useCase.title}</CardTitle>
                </CardHeader>
                <CardContent className="flex-grow">
                  <p className="text-sm text-muted-foreground">{useCase.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
          <div className="text-center mt-12">
            <Button size="lg" variant="default" asChild>
              <Link href="/vps-offerings">Explore Our VPS Plans</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Our Top VPS Plans Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold text-center text-primary mb-12">
            Our Top VPS Plans
          </h2>
          {topPlans.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {topPlans.map((plan) => (
                <VpsOfferingCard
                  key={plan.id}
                  plan={plan}
                  buttonText="Learn More"
                  buttonHref={`/vps-offerings#${plan.id}`}
                />
              ))}
            </div>
          ) : (
            <p className="text-center text-muted-foreground text-lg py-10">
              VPS plans are currently being updated. Please check back soon!
            </p>
          )}
          <div className="text-center mt-12">
            <Button variant="outline" asChild>
              <Link href="/vps-offerings">View All Plans</Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
