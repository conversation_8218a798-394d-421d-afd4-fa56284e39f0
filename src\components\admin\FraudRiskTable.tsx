"use client";

import { useState } from 'react';
import { Card, CardHeader, CardTitle, CardContent, CardFooter, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import type { Order } from "@/types";
import { ShieldAlert, CheckCircle, AlertCircle, HelpCircle, Bot, Loader2, Ban, ThumbsUp, ThumbsDown, ShieldCheck, MoreVertical, CalendarDays, User, Server, IndianRupee } from "lucide-react";
import { updateOrderStatus } from '@/lib/actions/adminActions';
import { performFraudCheck } from '@/lib/actions/adminActions'; // Ensure correct import for performFraudCheck
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from '@/lib/utils';

interface FraudRiskTableProps {
  orders: Order[];
}

interface FraudRiskOrder extends Order {
  isLoading?: boolean;
}

export function FraudRiskTable({ orders: initialOrders }: FraudRiskTableProps) {
  const [orders, setOrders] = useState<FraudRiskOrder[]>(initialOrders.map(o => ({ ...o, isLoading: false })));
  const [isProcessingAction, setIsProcessingAction] = useState<string | null>(null);
  const [viewingReportForOrderId, setViewingReportForOrderId] = useState<string | null>(null);
  const { toast } = useToast();

  const refreshOrderInState = (updatedOrder: Order) => {
    setOrders(prevOrders => prevOrders.map(o => o.id === updatedOrder.id ? { ...o, ...updatedOrder, isLoading: false } : o));
    setIsProcessingAction(null);
  };

  const handleAnalyzeOrder = async (orderId: string) => {
    setOrders(prevOrders => prevOrders.map(o => o.id === orderId ? { ...o, isLoading: true } : o));
    setIsProcessingAction(orderId);

    const result = await performFraudCheck(orderId);

    if ('error' in result) {
      toast({
        title: "Fraud Analysis Failed",
        description: result.error,
        variant: "error",
      });
      setOrders(prevOrders => prevOrders.map(o => o.id === orderId ? { ...o, isLoading: false } : o));
      setIsProcessingAction(null);
      return;
    }

    toast({
      title: "Fraud Analysis Complete",
      description: `Order ${orderId} analyzed. Review report and take action.`,
      variant: "success",
    });

    // Fetch the latest order state from server after fraud check updates it in JSON
    // This ensures we have the fraudAnalysis object for the dialog and correct status
    const updatedOrdersList = orders.map((o) => {
      if (o.id === orderId) {
        // The performFraudCheck action now updates the order in orders.json with fraudAnalysis
        // So, we merge the result from the AI directly here for immediate UI update.
        return {
          ...o,
          fraudAnalysis: { ...result, analyzedAt: new Date().toISOString() },
          isLoading: false
        };
      }
      return o;
    });
    setOrders(updatedOrdersList);
    setIsProcessingAction(null);
  };

  const handleAdminOrderAction = async (orderId: string, newStatus: Order['status'], newVpsStatus?: Order['vpsStatus']) => {
    setIsProcessingAction(orderId);
    const result = await updateOrderStatus(orderId, newStatus, newVpsStatus);

    if (result.success && result.order) {
      toast({ title: "Order Status Updated", description: `Order ${orderId} status set to ${newStatus}.`, variant: "success" });
      refreshOrderInState(result.order);
    } else {
      toast({ title: "Action Failed", description: result.error || "Could not update order.", variant: "error" });
      setIsProcessingAction(null);
    }
  };

  const getRiskBadge = (analysis?: Order['fraudAnalysis']) => {
    if (!analysis?.fraudRiskScore) return <Badge variant="outline" className="bg-gray-100 text-gray-600 text-xs px-2 py-1"><HelpCircle className="mr-1 h-3 w-3" /> Not Analyzed</Badge>;
    const score = analysis.fraudRiskScore;
    if (score > 0.7) return <Badge variant="destructive" className="text-xs px-2 py-1"><AlertCircle className="mr-1 h-3 w-3" /> High Risk ({score.toFixed(2)})</Badge>;
    if (score > 0.3) return <Badge variant="secondary" className="bg-yellow-100 text-yellow-700 border-yellow-300 text-xs px-2 py-1"><ShieldAlert className="mr-1 h-3 w-3" /> Medium Risk ({score.toFixed(2)})</Badge>;
    return <Badge variant="secondary" className="bg-green-100 text-green-700 border-green-300 text-xs px-2 py-1"><CheckCircle className="mr-1 h-3 w-3" /> Low Risk ({score.toFixed(2)})</Badge>;
  };

  const getStatusBadgeVariant = (status: Order['status']) => {
    switch (status) {
      case 'active': return 'default';
      case 'pending': return 'secondary';
      case 'cancelled': return 'destructive';
      case 'fraud_review': return 'outline';
      case 'processing': return 'secondary';
      default: return 'outline';
    }
  };

  return (
    <>
      {orders.length === 0 ? (
        <div className="text-center py-12">
          <ShieldCheck className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-xl font-semibold text-primary mb-2">No Orders to Display</h3>
          <p className="text-muted-foreground">There are currently no orders in the system requiring fraud review or matching the current filters.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {orders.map((order) => (
            <Card key={order.id} className={cn("shadow-md flex flex-col", order.fraudAnalysis?.isFraudulent ? 'border-destructive border-2' : '')}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg font-semibold text-primary">
                      Order ID: <span className="font-mono text-base">{order.id}</span>
                    </CardTitle>
                    <CardDescription className="text-xs text-muted-foreground">
                      <User className="inline-block h-3 w-3 mr-1" /> {order.userEmail || 'N/A'}
                    </CardDescription>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8" disabled={isProcessingAction === order.id || order.isLoading}>
                        {(isProcessingAction === order.id || order.isLoading) ? <Loader2 className="h-4 w-4 animate-spin" /> : <MoreVertical className="h-4 w-4" />}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={() => handleAnalyzeOrder(order.id)}
                        disabled={order.isLoading || isProcessingAction === order.id}
                      >
                        <Bot className="mr-2 h-4 w-4" />
                        {order.fraudAnalysis ? 'Re-Analyze Risk' : 'Analyze Risk'}
                      </DropdownMenuItem>

                      {order.fraudAnalysis && (
                        <>
                          <DropdownMenuItem onSelect={() => setViewingReportForOrderId(order.id)}>
                            <ShieldAlert className="mr-2 h-4 w-4" /> View Report
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuLabel>Order Actions</DropdownMenuLabel>
                          {order.status !== 'active' && order.status !== 'cancelled' && (
                            <DropdownMenuItem
                              onClick={() => handleAdminOrderAction(order.id, 'active', 'running')}
                              disabled={isProcessingAction === order.id}
                              className="text-green-600 focus:text-green-700 focus:bg-green-50"
                            >
                              <ThumbsUp className="mr-2 h-4 w-4" /> Approve Order
                            </DropdownMenuItem>
                          )}
                          {order.status !== 'cancelled' && (
                            <DropdownMenuItem
                              onClick={() => handleAdminOrderAction(order.id, 'cancelled', 'suspended')}
                              disabled={isProcessingAction === order.id}
                              className="text-red-600 focus:text-red-700 focus:bg-red-50"
                            >
                              <ThumbsDown className="mr-2 h-4 w-4" /> Mark Fraud & Cancel
                            </DropdownMenuItem>
                          )}
                          {order.status !== 'fraud_review' && order.status !== 'cancelled' && order.status !== 'active' && (
                            <DropdownMenuItem
                              onClick={() => handleAdminOrderAction(order.id, 'fraud_review', 'suspended')}
                              disabled={isProcessingAction === order.id}
                              className="text-yellow-600 focus:text-yellow-700 focus:bg-yellow-50"
                            >
                              <Ban className="mr-2 h-4 w-4" /> Set for Manual Review
                            </DropdownMenuItem>
                          )}
                        </>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent className="space-y-2 text-sm flex-grow">
                <div className="flex flex-col text-left sm:flex-row sm:justify-between sm:items-baseline">
                  <span className="text-sm text-muted-foreground flex items-center"><Server className="mr-1 h-3 w-3" /> Plan:</span>
                  <span className="text-sm font-medium break-words">{order.vpsPlanName || order.planId}</span>
                </div>
                <div className="flex flex-col text-left sm:flex-row sm:justify-between sm:items-baseline">
                  <span className="text-sm text-muted-foreground flex items-center"><IndianRupee className="mr-1 h-3 w-3" /> Amount:</span>
                  <span className="text-sm font-medium break-words">₹{order.totalAmount.toFixed(2)}</span>
                </div>
                <div className="flex flex-col text-left sm:flex-row sm:justify-between sm:items-baseline">
                  <span className="text-sm text-muted-foreground flex items-center"><CalendarDays className="mr-1 h-3 w-3" /> Date:</span>
                  <span className="text-sm font-medium break-words">{new Date(order.orderDate).toLocaleDateString()}</span>
                </div>
              </CardContent>
              <CardFooter className="border-t pt-4 flex flex-col items-start gap-3 sm:flex-row sm:justify-between sm:items-center sm:gap-4">
                <div>
                  <span className="text-xs text-muted-foreground mr-1">Status: </span>
                  <Badge variant={getStatusBadgeVariant(order.status)} className="capitalize text-xs px-2 py-1">
                    {order.status.replace('_', ' ')}
                  </Badge>
                </div>
                <div>
                  <span className="text-xs text-muted-foreground mr-1">Risk: </span>
                  {getRiskBadge(order.fraudAnalysis)}
                </div>
              </CardFooter>

              {order.fraudAnalysis && (
                <Dialog
                  open={viewingReportForOrderId === order.id}
                  onOpenChange={(isOpen) => { if (!isOpen) setViewingReportForOrderId(null); }}
                >
                  <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                      <DialogTitle>Fraud Analysis Report - {order.id}</DialogTitle>
                      <DialogDescription>
                        Analyzed on: {order.fraudAnalysis.analyzedAt ? new Date(order.fraudAnalysis.analyzedAt).toLocaleString() : 'N/A'}
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-3 py-4 text-sm">
                      <p><strong>Is Fraudulent (AI):</strong> {order.fraudAnalysis.isFraudulent ? 'Yes' : 'No'}</p>
                      <p><strong>Risk Score:</strong> {order.fraudAnalysis.fraudRiskScore.toFixed(3)}</p>
                      <p><strong>AI Recommendation:</strong> {order.fraudAnalysis.recommendation}</p>
                      <div>
                        <strong>Risk Factors (AI):</strong>
                        {order.fraudAnalysis.riskFactors.length > 0 ? (
                          <ul className="list-disc list-inside ml-4 mt-1">
                            {order.fraudAnalysis.riskFactors.map((factor, i) => <li key={i}>{factor}</li>)}
                          </ul>
                        ) : (<p className="ml-4 text-muted-foreground">None identified by AI.</p>)}
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              )}
            </Card>
          ))}
        </div>
      )}
    </>
  );
}
