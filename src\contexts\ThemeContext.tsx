"use client";

import type { ReactNode } from 'react';
import React, { createContext, useState, useMemo, useCallback, useEffect } from 'react';
import type { AppTheme, ThemeSettings } from '@/types';
import {
  getAvailableThemes as fetchAvailableThemes,
  getActiveThemeId as fetchGlobalActiveThemeId,
  setActiveThemeId as setGlobalActiveThemeId,
  updateThemeSettings as saveThemeSettingsToServer,
  deleteTheme as deleteThemeFromServer,
  createTheme as createNewThemeOnServer
} from '@/lib/actions/themeActions';
import { useToast } from '@/hooks/use-toast';

// Define a more specific type for the result of saveCurrentThemeSettings
interface SaveThemeSettingsResult {
  success: boolean;
  themes?: AppTheme[];
  error?: string;
  fieldErrors?: Partial<Record<keyof ThemeSettings, string[]>>;
}

interface DeleteThemeResult {
  success: boolean;
  themes?: AppTheme[];
  error?: string;
}

interface CreateThemeResult {
  success: boolean;
  themes?: AppTheme[];
  error?: string;
  fieldErrors?: Partial<Record<'name' | 'settings', any>>; // Simplified for context
}

interface ThemeContextType {
  theme: AppTheme;
  setTheme: (themeId: string) => void;
  availableThemes: AppTheme[];
  isLoadingThemes: boolean;
  saveCurrentThemeSettings: (updatedSettings: ThemeSettings) => Promise<SaveThemeSettingsResult>;
  deleteTheme: (themeId: string) => Promise<DeleteThemeResult>;
  addNewTheme: (themeName: string, themeSettings: ThemeSettings) => Promise<CreateThemeResult>;
}

export const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

const THEME_STORAGE_KEY = 'skyhosting-active-theme-id-session';
const FALLBACK_DEFAULT_THEME_ID = 'default';

export const initialDefaultTheme: AppTheme = {
  id: FALLBACK_DEFAULT_THEME_ID,
  name: 'Loading Theme...',
  settings: {
    background: "0 0% 94%",
    foreground: "210 29% 10%",
    primary: "210 29% 24%",
    primaryForeground: "0 0% 98%",
    secondary: "210 30% 88%",
    secondaryForeground: "210 29% 24%",
    accent: "180 100% 25%",
    accentForeground: "0 0% 98%",
  }
};

export function ThemeProvider({ children }: { children: ReactNode }) {
  const [currentThemeId, setCurrentThemeId] = useState<string | null>(null);
  const [availableThemes, setAvailableThemes] = useState<AppTheme[]>([initialDefaultTheme]);
  const [isLoadingThemes, setIsLoadingThemes] = useState(true);
  const [isMounted, setIsMounted] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    async function loadInitialThemeSettings() {
      if (!isMounted) return;

      setIsLoadingThemes(true);
      try {
        const [themesFromServer, globalActiveThemeIdFromServer] = await Promise.all([
          fetchAvailableThemes(),
          fetchGlobalActiveThemeId()
        ]);

        const validThemes = themesFromServer.length > 0 ? themesFromServer : [initialDefaultTheme];
        setAvailableThemes(validThemes);

        let themeToApplyId = globalActiveThemeIdFromServer;

        if (!validThemes.some(t => t.id === themeToApplyId)) {
          console.warn(`Global theme ID "${themeToApplyId}" not found in available themes. Falling back.`);
          themeToApplyId = validThemes[0]?.id || FALLBACK_DEFAULT_THEME_ID;
        }

        setCurrentThemeId(themeToApplyId);
        if (typeof window !== 'undefined') {
          sessionStorage.setItem(THEME_STORAGE_KEY, themeToApplyId);
        }

      } catch (error) {
        console.error("Failed to load initial theme settings:", error);
        setAvailableThemes([initialDefaultTheme]);
        setCurrentThemeId(FALLBACK_DEFAULT_THEME_ID);
        if (typeof window !== 'undefined') {
          sessionStorage.setItem(THEME_STORAGE_KEY, FALLBACK_DEFAULT_THEME_ID);
        }
      } finally {
        setIsLoadingThemes(false);
      }
    }
    loadInitialThemeSettings();
  }, [isMounted]);

  const activeTheme = useMemo(() => {
    if (isLoadingThemes || !currentThemeId) return initialDefaultTheme;
    return availableThemes.find(t => t.id === currentThemeId) || availableThemes.find(t => t.id === FALLBACK_DEFAULT_THEME_ID) || initialDefaultTheme;
  }, [currentThemeId, availableThemes, isLoadingThemes]);

  const applyThemeSettings = useCallback((themeSettings: ThemeSettings) => {
    if (typeof window === 'undefined') return;
    const root = document.documentElement;
    if (!root) return;

    Object.entries(themeSettings).forEach(([key, value]) => {
      const cssVarName = `--${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
      root.style.setProperty(cssVarName, value);
    });
  }, []);

  useEffect(() => {
    if (activeTheme && activeTheme.settings && isMounted && !isLoadingThemes) {
      applyThemeSettings(activeTheme.settings);
    }
  }, [activeTheme, applyThemeSettings, isMounted, isLoadingThemes]);

  const setTheme = useCallback(async (themeId: string) => {
    const newTheme = availableThemes.find(t => t.id === themeId);
    if (newTheme) {
      const result = await setGlobalActiveThemeId(themeId);
      if (result.success) {
        setCurrentThemeId(newTheme.id);
        if (typeof window !== 'undefined') {
          sessionStorage.setItem(THEME_STORAGE_KEY, newTheme.id);
        }
        toast({ title: "Theme Updated", description: `Site theme changed to ${newTheme.name}.`, variant: "success" });
      } else {
        toast({ title: "Theme Update Failed", description: result.error || "Could not save theme choice to server.", variant: "error" });
      }
    } else {
      toast({ title: "Theme Not Found", description: `Could not apply theme with ID ${themeId}.`, variant: "error" });
    }
  }, [availableThemes, toast]);

  const saveCurrentThemeSettings = useCallback(async (updatedSettings: ThemeSettings): Promise<SaveThemeSettingsResult> => {
    if (!activeTheme || activeTheme.id === initialDefaultTheme.id) {
      const errorMsg = "Cannot save settings for a loading or fallback theme.";
      toast({ title: "Error", description: errorMsg, variant: "error" });
      return { success: false, error: errorMsg };
    }
    const result = await saveThemeSettingsToServer(activeTheme.id, updatedSettings);
    if (result.success && result.themes) {
      setAvailableThemes(result.themes);
      const updatedCurrentTheme = result.themes.find(t => t.id === activeTheme.id);
      if (updatedCurrentTheme) {
        applyThemeSettings(updatedCurrentTheme.settings); // Apply new settings if current theme was edited
      }
      toast({ title: "Theme Settings Saved", description: `Colors for ${activeTheme.name} have been updated.`, variant: "success" });
      return { success: true, themes: result.themes };
    } else {
      // If update fails, re-apply original settings of the active theme to revert optimistic UI changes.
      applyThemeSettings(activeTheme.settings);
      toast({ title: "Save Failed", description: result.error || "Could not save theme settings to server.", variant: "error" });
      return { success: false, error: result.error, fieldErrors: result.fieldErrors };
    }
  }, [activeTheme, toast, applyThemeSettings]);

  const deleteTheme = useCallback(async (themeId: string): Promise<DeleteThemeResult> => {
    const result = await deleteThemeFromServer(themeId);
    if (result.success && result.themes) {
      setAvailableThemes(result.themes);
      // If the deleted theme was the active one, fallback to default (though server action should prevent this for global active)
      if (currentThemeId === themeId) {
        setCurrentThemeId(FALLBACK_DEFAULT_THEME_ID);
        if (typeof window !== 'undefined') {
          sessionStorage.setItem(THEME_STORAGE_KEY, FALLBACK_DEFAULT_THEME_ID);
        }
      }
      toast({ title: "Theme Deleted", description: `Theme has been deleted.`, variant: "success" });
      return { success: true, themes: result.themes };
    } else {
      toast({ title: "Deletion Failed", description: result.error || "Could not delete theme.", variant: "error" });
      return { success: false, error: result.error };
    }
  }, [currentThemeId, toast]);

  const addNewTheme = useCallback(async (themeName: string, themeSettings: ThemeSettings): Promise<CreateThemeResult> => {
    const result = await createNewThemeOnServer(themeName, themeSettings);
    if (result.success && result.themes) {
      setAvailableThemes(result.themes);
      toast({ title: "Theme Created", description: `New theme "${themeName}" has been added.`, variant: "success" });
      return { success: true, themes: result.themes };
    } else {
      toast({ title: "Theme Creation Failed", description: result.error || "Could not create new theme.", variant: "error" });
      return { success: false, error: result.error, fieldErrors: result.fieldErrors as any };
    }
  }, [toast]);

  const contextValue = useMemo(() => ({
    theme: activeTheme,
    setTheme,
    availableThemes,
    isLoadingThemes,
    saveCurrentThemeSettings,
    deleteTheme,
    addNewTheme,
  }), [activeTheme, setTheme, availableThemes, isLoadingThemes, saveCurrentThemeSettings, deleteTheme, addNewTheme]);

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
}

export const useTheme = () => {
  const context = React.useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

