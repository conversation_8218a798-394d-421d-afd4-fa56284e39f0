'use server';

import type { AppTheme, ThemeSettings } from '@/types';
import { readJsonFile, writeJsonFile } from '@/lib/jsonUtils';
import * as z from "zod";

const THEMES_FILE = 'themes.json';
const SITE_SETTINGS_FILE = 'site-settings.json';

interface SiteSettings {
  activeThemeId: string;
}

const hslColorStringSchema = z.string().regex(
  /^\d{1,3}\s\d{1,3}%\s\d{1,3}%$/,
  "Must be valid HSL format (e.g., '210 100% 50%')"
);

const themeSettingsSchema = z.object({
  background: hslColorStringSchema,
  foreground: hslColorStringSchema,
  primary: hslColorStringSchema,
  primaryForeground: hslColorStringSchema,
  secondary: hslColorStringSchema,
  secondaryForeground: hslColorStringSchema,
  accent: hslColorStringSchema,
  accentForeground: hslColorStringSchema,
});

const newThemeFormSchema = z.object({
  name: z.string().min(3, "Theme name must be at least 3 characters.").max(30, "Theme name cannot exceed 30 characters."),
  settings: themeSettingsSchema,
});


export async function getAvailableThemes(): Promise<AppTheme[]> {
  try {
    return await readJsonFile<AppTheme>(THEMES_FILE);
  } catch (error) {
    console.error('Error in getAvailableThemes:', error);
    return [
      {
        id: 'default-fallback-error',
        name: 'Default (Error)',
        settings: {
          background: "0 0% 94%",
          foreground: "0 0% 10%",
          primary: "210 100% 50%",
          primaryForeground: "0 0% 100%",
          secondary: "0 0% 85%",
          secondaryForeground: "0 0% 10%",
          accent: "0 100% 50%",
          accentForeground: "0 0% 100%",
        },
      },
    ];
  }
}

export async function getActiveThemeId(): Promise<string> {
  try {
    const settingsArray = await readJsonFile<SiteSettings>(SITE_SETTINGS_FILE);
    if (settingsArray.length > 0 && settingsArray[0].activeThemeId) {
      return settingsArray[0].activeThemeId;
    }
    console.warn(`${SITE_SETTINGS_FILE} is empty or malformed. Falling back to 'default' theme ID.`);
    return 'default';

  } catch (error) {
    console.error(`Error reading ${SITE_SETTINGS_FILE}:`, error);
    return 'default';
  }
}

export async function setActiveThemeId(themeId: string): Promise<{ success: boolean; error?: string }> {
  try {
    const newSettings: SiteSettings = { activeThemeId: themeId };
    await writeJsonFile<SiteSettings>(SITE_SETTINGS_FILE, [newSettings]);
    return { success: true };
  } catch (error) {
    console.error(`Error writing to ${SITE_SETTINGS_FILE}:`, error);
    return { success: false, error: error instanceof Error ? error.message : 'Failed to set active theme.' };
  }
}

export async function updateThemeSettings(
  themeId: string,
  newSettings: ThemeSettings
): Promise<{ success: boolean; themes?: AppTheme[]; error?: string; fieldErrors?: Partial<Record<keyof ThemeSettings, string[]>> }> {
  const validationResult = themeSettingsSchema.safeParse(newSettings);
  if (!validationResult.success) {
    return {
      success: false,
      error: "Validation failed. Please check the HSL color values.",
      fieldErrors: validationResult.error.flatten().fieldErrors as Partial<Record<keyof ThemeSettings, string[]>>
    };
  }

  const validatedSettings = validationResult.data;

  try {
    let themes = await readJsonFile<AppTheme>(THEMES_FILE);
    const themeIndex = themes.findIndex(t => t.id === themeId);

    if (themeIndex === -1) {
      return { success: false, error: `Theme with ID "${themeId}" not found.` };
    }

    themes[themeIndex].settings = validatedSettings;
    await writeJsonFile<AppTheme>(THEMES_FILE, themes);
    return { success: true, themes: themes };
  } catch (error) {
    console.error(`Error updating theme settings for ID "${themeId}":`, error);
    return { success: false, error: error instanceof Error ? error.message : 'Failed to update theme settings.' };
  }
}

export async function deleteTheme(themeId: string): Promise<{ success: boolean; themes?: AppTheme[]; error?: string }> {
  if (themeId === 'default') {
    return { success: false, error: "The default theme cannot be deleted." };
  }

  try {
    const globalActiveThemeId = await getActiveThemeId();
    if (themeId === globalActiveThemeId) {
      return { success: false, error: "Cannot delete the currently active global theme. Please switch to another theme first." };
    }

    let themes = await readJsonFile<AppTheme>(THEMES_FILE);
    const initialLength = themes.length;
    themes = themes.filter(t => t.id !== themeId);

    if (themes.length === initialLength) {
      return { success: false, error: `Theme with ID "${themeId}" not found for deletion.` };
    }

    await writeJsonFile<AppTheme>(THEMES_FILE, themes);
    return { success: true, themes: themes };
  } catch (error) {
    console.error(`Error deleting theme with ID "${themeId}":`, error);
    return { success: false, error: error instanceof Error ? error.message : 'Failed to delete theme.' };
  }
}

export async function createTheme(
  themeName: string,
  themeSettings: ThemeSettings
): Promise<{ success: boolean; themes?: AppTheme[]; error?: string; fieldErrors?: Partial<Record<keyof z.infer<typeof newThemeFormSchema>, string[] | undefined> & { settings?: Partial<Record<keyof ThemeSettings, string[]>> }> }> {
  const validationResult = newThemeFormSchema.safeParse({ name: themeName, settings: themeSettings });

  if (!validationResult.success) {
    const flattenedErrors = validationResult.error.flatten();
    const fieldErrors: any = {}; // Using any temporarily
    if (flattenedErrors.fieldErrors.name) fieldErrors.name = flattenedErrors.fieldErrors.name;
    if (flattenedErrors.fieldErrors.settings) fieldErrors.settings = flattenedErrors.fieldErrors.settings;


    return {
      success: false,
      error: "Validation failed. Please check the theme name and color values.",
      fieldErrors: fieldErrors,
    };
  }

  const { name: validatedName, settings: validatedSettings } = validationResult.data;

  try {
    const themes = await readJsonFile<AppTheme>(THEMES_FILE);

    if (themes.some(t => t.name.toLowerCase() === validatedName.toLowerCase())) {
      return {
        success: false,
        error: "A theme with this name already exists.",
        fieldErrors: { name: ["A theme with this name already exists."] }
      };
    }

    const newThemeId = validatedName.toLowerCase().replace(/\s+/g, '-') + '-' + Date.now().toString(36).slice(-4);

    const newTheme: AppTheme = {
      id: newThemeId,
      name: validatedName,
      settings: validatedSettings,
    };

    themes.push(newTheme);
    await writeJsonFile<AppTheme>(THEMES_FILE, themes);
    return { success: true, themes };
  } catch (error) {
    console.error(`Error creating theme "${validatedName}":`, error);
    return { success: false, error: error instanceof Error ? error.message : 'Failed to create theme.' };
  }
}

