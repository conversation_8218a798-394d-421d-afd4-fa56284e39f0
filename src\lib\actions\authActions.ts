'use server';

import type { User, AppSession } from '@/types';
import { logActivity } from '@/lib/actions/activityLogActions';
import { readJsonFile, writeJsonFile } from '@/lib/jsonUtils';
import * as z from "zod";

// Interface representing the user structure as stored in users.json
interface UserWithStoredPassword extends User {
  password?: string; // This field holds the placeholder like "[hashed_password_for_user_id_XXX]"
}

const USERS_FILE = 'users.json';
const SESSIONS_FILE = 'sessions.json';

// Schema for user profile updates
const userProfileUpdateSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters.").max(50, "Name cannot exceed 50 characters.").optional(),
  email: z.string().email("Invalid email address.").optional(),
  is2FAEnabled: z.boolean().optional(),
});

// Schema for signup
const signupFormSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }).max(50, { message: "Name cannot exceed 50 characters." }),
  email: z.string().email({ message: "Invalid email address." }),
  password: z.string().min(8, { message: "Password must be at least 8 characters." }),
});

// Schema for login (basic format validation)
const loginFormSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address." }),
  password: z.string().min(1, { message: "Password cannot be empty." }),
});

export interface AuthActionResult {
  sessionId?: string;
  user?: User;
  error?: string;
  fieldErrors?: Partial<Record<keyof z.infer<typeof signupFormSchema> | keyof z.infer<typeof loginFormSchema>, string[]>>;
  success?: boolean;
}


export async function loginUser(email: string, passwordInput: string): Promise<AuthActionResult> {
  const validationResult = loginFormSchema.safeParse({ email, password: passwordInput });
  if (!validationResult.success) {
    return {
      success: false,
      error: "Validation failed. Please check the fields below.",
      fieldErrors: validationResult.error.flatten().fieldErrors as Partial<Record<keyof z.infer<typeof loginFormSchema>, string[]>>
    };
  }

  const users = await readJsonFile<UserWithStoredPassword>(USERS_FILE);
  const userRecord = users.find(u => u.email === email);

  if (!userRecord || !userRecord.password || passwordInput.length === 0) { // Basic check
    return { error: 'Invalid email or password.', success: false };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { password, ...userToReturn } = userRecord;

  try {
    let sessions = await readJsonFile<AppSession>(SESSIONS_FILE);
    sessions = sessions.filter(session => session.userId !== userToReturn.id); // Remove existing sessions for this user

    const sessionId = `sess_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
    const expiresAt = new Date(Date.now() + 60 * 60 * 1000).toISOString(); // 1 hour expiry

    const newSession: AppSession = {
      sessionId,
      userId: userToReturn.id,
      expiresAt,
    };

    sessions.push(newSession);
    await writeJsonFile<AppSession>(SESSIONS_FILE, sessions);

    await logActivity(
      userToReturn.id,
      'LOGIN_SUCCESS',
      `Successfully logged in.`,
      'LogIn'
    );

    return { sessionId, user: userToReturn, success: true };
  } catch (e) {
    console.error("Session creation error:", e);
    return { error: "Failed to create user session.", success: false };
  }
}

export async function signupUser(name: string, emailInput: string, passwordInput: string): Promise<AuthActionResult> {
  const validationResult = signupFormSchema.safeParse({ name, email: emailInput, password: passwordInput });

  if (!validationResult.success) {
    return {
      success: false,
      error: "Validation failed. Please check the fields below.",
      fieldErrors: validationResult.error.flatten().fieldErrors
    };
  }

  const validatedData = validationResult.data;

  let users = await readJsonFile<UserWithStoredPassword>(USERS_FILE);
  if (users.find(u => u.email === validatedData.email)) {
    return {
      success: false,
      error: 'User with this email already exists.',
      fieldErrors: { email: ["This email address is already in use."] }
    };
  }

  const newUserId = `user${Date.now()}${Math.floor(Math.random() * 1000)}`;
  const newUserRecord: UserWithStoredPassword = {
    id: newUserId,
    name: validatedData.name,
    email: validatedData.email,
    password: `[hashed_password_for_${newUserId}]`,
    role: 'user',
    is2FAEnabled: false,
    status: 'active',
  };

  users.push(newUserRecord);
  await writeJsonFile<UserWithStoredPassword>(USERS_FILE, users);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { password, ...userToReturn } = newUserRecord;

  try {
    let sessions = await readJsonFile<AppSession>(SESSIONS_FILE);
    sessions = sessions.filter(session => session.userId !== userToReturn.id); // Remove existing sessions

    const sessionId = `sess_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
    const expiresAt = new Date(Date.now() + 60 * 60 * 1000).toISOString(); // 1 hour expiry

    const newSession: AppSession = {
      sessionId,
      userId: userToReturn.id,
      expiresAt,
    };
    sessions.push(newSession);
    await writeJsonFile<AppSession>(SESSIONS_FILE, sessions);

    await logActivity(
      userToReturn.id,
      'LOGIN_SUCCESS',
      `Account created and logged in.`,
      'UserPlus'
    );

    return { sessionId, user: userToReturn, success: true };
  } catch (e) {
    console.error("Session creation error during signup:", e);
    return { success: false, error: "User created, but failed to create session." };
  }
}

interface UpdateUserPersistenceResult {
  success: boolean;
  user?: User;
  error?: string;
  fieldErrors?: Partial<Record<keyof z.infer<typeof userProfileUpdateSchema>, string[]>>;
}

export async function updateUserPersistence(userId: string, updates: Partial<User>): Promise<UpdateUserPersistenceResult> {
  const validationResult = userProfileUpdateSchema.safeParse(updates);
  if (!validationResult.success) {
    return {
      success: false,
      error: "Validation failed. Please check the fields below.",
      fieldErrors: validationResult.error.flatten().fieldErrors
    };
  }

  const validatedUpdates = validationResult.data;

  let users = await readJsonFile<UserWithStoredPassword>(USERS_FILE);
  const userIndex = users.findIndex(u => u.id === userId);

  if (userIndex === -1) {
    return { success: false, error: 'User not found.' };
  }

  const oldUserData = { ...users[userIndex] };

  if (validatedUpdates.email && validatedUpdates.email !== oldUserData.email) {
    if (users.some(u => u.email === validatedUpdates.email && u.id !== userId)) {
      return {
        success: false,
        error: 'This email address is already in use by another account.',
        fieldErrors: { email: ['This email address is already in use by another account.'] }
      };
    }
  }

  const updatedUserRecord: UserWithStoredPassword = {
    ...users[userIndex],
    ...validatedUpdates,
  };
  users[userIndex] = updatedUserRecord;

  await writeJsonFile<UserWithStoredPassword>(USERS_FILE, users);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { password, ...userWithoutPassword } = updatedUserRecord;

  let updateDescription = "Profile details updated.";
  if (validatedUpdates.is2FAEnabled !== undefined && validatedUpdates.is2FAEnabled !== oldUserData.is2FAEnabled) {
    updateDescription = `Two-Factor Authentication ${validatedUpdates.is2FAEnabled ? 'enabled' : 'disabled'}.`;
  } else if (validatedUpdates.name && validatedUpdates.name !== oldUserData.name) {
    updateDescription = "Name updated.";
  } else if (validatedUpdates.email && validatedUpdates.email !== oldUserData.email) {
    updateDescription = "Email address updated.";
  }

  await logActivity(
    userId,
    'PROFILE_UPDATED',
    updateDescription,
    'UserCog'
  );

  return { success: true, user: userWithoutPassword };
}


export async function validateSession(sessionId: string | null): Promise<{ user: User | null; error?: string }> {
  if (!sessionId) {
    return { user: null, error: "No session ID provided." };
  }
  try {
    let sessions = await readJsonFile<AppSession>(SESSIONS_FILE);
    const sessionIndex = sessions.findIndex(s => s.sessionId === sessionId);

    if (sessionIndex === -1) {
      return { user: null, error: "Session not found." };
    }

    const session = sessions[sessionIndex];

    if (new Date() >= new Date(session.expiresAt)) {
      sessions.splice(sessionIndex, 1);
      await writeJsonFile<AppSession>(SESSIONS_FILE, sessions);
      return { user: null, error: "Session expired." };
    }

    const users = await readJsonFile<UserWithStoredPassword>(USERS_FILE);
    const userRecord = users.find(u => u.id === session.userId);

    if (!userRecord) {
      sessions.splice(sessionIndex, 1);
      await writeJsonFile<AppSession>(SESSIONS_FILE, sessions);
      return { user: null, error: "User for session not found." };
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...userToReturn } = userRecord;
    return { user: userToReturn };

  } catch (error) {
    console.error("Error validating session:", error);
    return { user: null, error: "Failed to validate session." };
  }
}

export async function logoutUser(sessionId: string | null): Promise<{ success: boolean; error?: string }> {
  if (!sessionId) {
    return { success: false, error: "No session ID provided for logout." };
  }
  try {
    let sessions = await readJsonFile<AppSession>(SESSIONS_FILE);
    const initialLength = sessions.length;
    sessions = sessions.filter(s => s.sessionId !== sessionId);

    if (sessions.length < initialLength) {
      await writeJsonFile<AppSession>(SESSIONS_FILE, sessions);
    }
    return { success: true };
  } catch (error) {
    console.error("Error logging out user (server-side):", error);
    return { success: false, error: "Failed to invalidate session on server." };
  }
}
