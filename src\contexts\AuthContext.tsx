"use client";

import type { ReactNode } from 'react';
import { createContext, useState, useMemo, useCallback, useEffect } from 'react';
import type { User, AppSession } from '@/types'; // Ensure AppSession is imported if needed elsewhere, or remove if not.
import { updateUserPersistence, validateSession, logoutUser as serverLogoutUser } from '@/lib/actions/authActions'; 
import { useToast } from '@/hooks/use-toast';

const SESSION_STORAGE_KEY = 'skyhosting-session-id';

// Define a more specific type for the result of updateUser
interface UpdateUserResult {
  success: boolean;
  user?: User;
  error?: string;
  fieldErrors?: Partial<Record<keyof User, string[]>>;
}

interface AuthContextType {
  user: User | null;
  sessionId: string | null;
  login: (sessionId: string, userData: User) => void; 
  logout: () => void;
  isAuthenticated: boolean;
  isAdmin: boolean;
  updateUser: (updatedUserData: Partial<User>) => Promise<UpdateUserResult | { success: false; error: string; }>; // Return type updated
  isLoadingAuth: boolean; 
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [isLoadingAuth, setIsLoadingAuth] = useState(true);

  useEffect(() => {
    const attemptValidateSession = async () => {
      setIsLoadingAuth(true);
      const storedSessionId = sessionStorage.getItem(SESSION_STORAGE_KEY);
      if (storedSessionId) {
        const result = await validateSession(storedSessionId);
        if (result.user) {
          setUser(result.user);
          setSessionId(storedSessionId);
        } else {
          sessionStorage.removeItem(SESSION_STORAGE_KEY); 
          setUser(null);
          setSessionId(null);
          if (result.error && result.error !== "Session not found." && result.error !== "No session ID provided.") {
            console.warn("Session validation failed:", result.error);
          }
        }
      }
      setIsLoadingAuth(false);
    };
    attemptValidateSession();
  }, []);


  const login = (newSessionId: string, userData: User) => {
    sessionStorage.setItem(SESSION_STORAGE_KEY, newSessionId);
    setSessionId(newSessionId);
    setUser(userData);
  };

  const logout = useCallback(async () => {
    const currentSessionId = sessionId || sessionStorage.getItem(SESSION_STORAGE_KEY);
    if (currentSessionId) {
      await serverLogoutUser(currentSessionId); 
    }
    sessionStorage.removeItem(SESSION_STORAGE_KEY);
    setSessionId(null);
    setUser(null);
  }, [sessionId]);

  const updateUser = useCallback(async (updatedPartialData: Partial<User>): Promise<UpdateUserResult | { success: false; error: string; }> => {
    if (!user) return { success: false, error: "User not authenticated." };

    const oldUser = { ...user }; 

    const result = await updateUserPersistence(user.id, updatedPartialData);
    
    if (result.success && result.user) {
      setUser(result.user); 
      return { success: true, user: result.user };
    } else {
      setUser(oldUser); // Revert optimistic update on error
      return { success: false, error: result.error, fieldErrors: result.fieldErrors as any }; // Pass through fieldErrors
    }
  }, [user]);

  const isAuthenticated = !!user && !!sessionId && !isLoadingAuth;
  const isAdmin = isAuthenticated && user?.role === 'admin';

  const contextValue = useMemo(() => ({
    user,
    sessionId,
    login,
    logout,
    isAuthenticated,
    isAdmin,
    updateUser,
    isLoadingAuth,
  }), [user, sessionId, isAuthenticated, isAdmin, updateUser, logout, isLoadingAuth]);

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}
