"use client"; 

import { UpiPaymentForm } from "@/components/payment/UpiPaymentForm";
import { Suspense, useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useRouter } from "next/navigation";
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { Loader2 } from "lucide-react";

export default function UpiPaymentPage() {
  const { isAuthenticated, isLoadingAuth } = useAuth(); // Added isLoadingAuth
  const router = useRouter();

  useEffect(() => {
    if (!isLoadingAuth) { // Wait for auth state to be determined
      if (isAuthenticated === false) { 
        router.push('/login?redirect=/payment/upi');
      }
    }
  }, [isAuthenticated, router, isLoadingAuth]);

  if (isLoadingAuth) { // Initial loading state for auth
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-200px)]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="mt-2 text-lg text-muted-foreground">Verifying authentication...</p>
      </div>
    );
  }
  
  if (isAuthenticated === false) { // Explicitly not authenticated, about to redirect
     return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-200px)]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="text-lg text-muted-foreground my-4">
          Redirecting to login...
        </p>
        <Button asChild><Link href="/login?redirect=/payment/upi">Go to Login</Link></Button>
      </div>
    );
  }

  // Authenticated, proceed to render payment form
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-[calc(100vh-200px)] text-lg text-muted-foreground">
        <Loader2 className="mr-2 h-5 w-5 animate-spin" />
        Loading payment details...
      </div>
    }>
      <div className="flex items-center justify-center py-12">
        <UpiPaymentForm />
      </div>
    </Suspense>
  );
}
