"use client";

import { useContext } from 'react';
import { AuthContext } from '@/contexts/AuthContext';

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  // The isAdmin logic is now directly handled within AuthContext based on user email.
  // The updateUser function is also passed through from the context.
  return {
    ...context,
  };
};
