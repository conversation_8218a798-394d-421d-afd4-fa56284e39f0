"use client";

import type { ReactNode } from 'react';
import { useAuth } from "@/hooks/useAuth";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { Loader2 } from 'lucide-react'; // For loading state

export default function AdminLayout({ children }: { children: ReactNode }) {
  const { isAuthenticated, isAdmin, isLoadingAuth } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoadingAuth) { // Only check after auth state is determined
      if (isAuthenticated === false) {
        router.push('/login');
      } else if (isAuthenticated === true && !isAdmin) {
        router.push('/dashboard');
      }
    }
  }, [isAuthenticated, isAdmin, router, isLoadingAuth]);

  if (isLoadingAuth) { // Show loading state while auth is being checked
    return (
      <div className="flex flex-col items-center justify-center flex-1 p-6 md:p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="mt-2 text-lg text-muted-foreground">Verifying access...</p>
      </div>
    );
  }

  if (!isAuthenticated || !isAdmin) { // If still not authenticated or not admin after loading
    return (
      <div className="flex flex-col items-center justify-center flex-1 p-6 md:p-8">
        <p className="text-lg text-muted-foreground mb-4">
          {isAuthenticated && !isAdmin ? "Access Denied. Redirecting to dashboard..." : "Redirecting to login..."}
        </p>
        <Button asChild><Link href={isAuthenticated && !isAdmin ? "/dashboard" : "/login"}>Go Back</Link></Button>
      </div>
    );
  }

  return (
    <div className="flex-1 p-6 md:p-8 bg-muted/20 rounded-lg shadow-inner">
      {children}
    </div>
  );
}
