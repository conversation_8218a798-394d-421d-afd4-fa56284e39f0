import { VpsOfferingCard } from '@/components/vps/VpsOfferingCard';
import type { Metadata } from 'next';
import { getAllVpsPlans } from '@/lib/actions/vpsPlanActions';
import type { VPSPlan } from '@/types';

export const metadata: Metadata = {
  title: 'VPS Offerings - SkyHosting',
  description: 'Explore our range of powerful and affordable VPS hosting plans.',
};

export default async function VpsOfferingsPage() {
  const vpsPlans: VPSPlan[] = await getAllVpsPlans();

  return (
    <div className="space-y-12">
      <section className="text-center py-12">
        <h1 className="text-4xl md:text-5xl font-bold text-primary mb-4">Our VPS Hosting Plans</h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Choose from a variety of VPS plans designed to meet your specific needs, from small projects to large-scale applications.
        </p>
      </section>

      <section>
        {vpsPlans.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {vpsPlans.map((plan) => (
              <VpsOfferingCard key={plan.id} plan={plan} />
            ))}
          </div>
        ) : (
          <p className="text-center text-muted-foreground text-lg py-10">
            No VPS plans are currently available. Please check back later.
          </p>
        )}
      </section>

      <section className="py-12 mt-16 bg-secondary/50 rounded-lg p-8">
        <h2 className="text-3xl font-bold text-primary text-center mb-8">All Plans Include</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-6 max-w-3xl mx-auto text-center">
          {[
            "99.9% Uptime Guarantee",
            "24/7 Customer Support",
            "Full Root Access",
            "Choice of OS",
            "Scalable Resources",
            "Secure Infrastructure",
          ].map((feature, index) => (
            <div key={index} className="p-4 bg-background rounded-md shadow">
              <p className="font-medium text-primary">{feature}</p>
            </div>
          ))}
        </div>
      </section>
    </div>
  );
}
