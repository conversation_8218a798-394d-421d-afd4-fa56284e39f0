'use server';

import type { Payment } from '@/types';
import { logActivity } from '@/lib/actions/activityLogActions';
import { readJsonFile, writeJsonFile } from '@/lib/jsonUtils';

const PAYMENTS_FILE = 'payments.json';

export async function getAllPayments(): Promise<Payment[]> {
  try {
    const allPayments = await readJsonFile<Payment>(PAYMENTS_FILE);
    return allPayments.sort((a, b) => new Date(b.paymentDate).getTime() - new Date(a.paymentDate).getTime());
  } catch (error) {
    console.error('Error fetching all payments:', error);
    return [];
  }
}

export async function updatePaymentStatus(paymentId: string, newStatus: 'completed' | 'failed'): Promise<{ success: boolean; payment?: Payment; error?: string }> {
  try {
    const payments = await readJsonFile<Payment>(PAYMENTS_FILE);
    const paymentIndex = payments.findIndex(p => p.id === paymentId);

    if (paymentIndex === -1) {
      return { success: false, error: 'Payment not found.' };
    }

    payments[paymentIndex].status = newStatus;

    await writeJsonFile<Payment>(PAYMENTS_FILE, payments);

    if (newStatus === 'completed') {
      await logActivity(
        payments[paymentIndex].userId,
        'PAYMENT_COMPLETED',
        `Payment ${paymentId} for order ${payments[paymentIndex].orderId} marked as completed.`,
        'CreditCard'
      );
    }

    return { success: true, payment: payments[paymentIndex] };
  } catch (error) {
    console.error('Error updating payment status:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to update payment status.';
    return { success: false, error: errorMessage };
  }
}

export async function getUserPayments(userId: string): Promise<Payment[]> {
  if (!userId) {
    console.warn('getUserPayments called without a userId.');
    return [];
  }
  try {
    const allPayments = await readJsonFile<Payment>(PAYMENTS_FILE);
    const userPayments = allPayments.filter(payment => payment.userId === userId);

    return userPayments.sort((a, b) => new Date(b.paymentDate).getTime() - new Date(a.paymentDate).getTime());
  } catch (error) {
    console.error(`Error fetching payments for user ${userId}:`, error);
    return [];
  }
}

interface CreatePaymentData {
  orderId: string;
  userId: string;
  amount: number;
  method: string;
  transactionId?: string;
  status: Payment['status'];
}

export async function createPaymentRecord(paymentData: CreatePaymentData): Promise<{ payment?: Payment; error?: string }> {
  if (!paymentData.userId) {
    return { error: "User ID is required to create a payment record." };
  }
  if (paymentData.amount <= 0 && paymentData.orderId.startsWith("PLAN-")) {
    return { error: "Payment amount must be greater than zero for new plan purchases." };
  }
  try {
    const payments = await readJsonFile<Payment>(PAYMENTS_FILE);
    const now = new Date().toISOString();
    const newPayment: Payment = {
      id: `PAY${Date.now()}${Math.floor(Math.random() * 1000)}`,
      ...paymentData,
      paymentDate: now,
    };
    payments.push(newPayment);
    await writeJsonFile<Payment>(PAYMENTS_FILE, payments);

    await logActivity(
      paymentData.userId,
      'PAYMENT_COMPLETED',
      `Payment details submitted for order ${paymentData.orderId} (UTR: ${paymentData.transactionId || 'N/A'}).`,
      'CreditCard'
    );

    return { payment: newPayment };
  } catch (error) {
    console.error("Error creating payment record:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to create payment record.";
    return { error: errorMessage };
  }
}

export async function countUserPaymentsByStatus(userId: string, status: Payment['status']): Promise<number> {
  if (!userId) {
    return 0;
  }
  try {
    const allPayments = await readJsonFile<Payment>(PAYMENTS_FILE);
    return allPayments.filter(payment => payment.userId === userId && payment.status === status).length;
  } catch (error) {
    console.error(`Error counting payments for user ${userId} with status ${status}:`, error);
    return 0;
  }
}
