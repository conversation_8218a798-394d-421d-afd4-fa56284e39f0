"use client";

import { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuSubContent,
  DropdownMenuPortal,
} from "@/components/ui/dropdown-menu";
import {
  Ticket as TicketIcon, MoreVertical, Eye, CheckCircle, Zap, XCircle,
  Loader2, RefreshCw, ChevronDown, Tag, User, CalendarDays,
  Filter, ChevronLeft, ChevronRight, Flag, AlignLeft
} from "lucide-react";
import type { SupportTicket, TicketStatus, TicketPriority } from "@/types";
import { useToast } from '@/hooks/use-toast';
import { getAllTickets, updateTicket, getTicketPriorities } from '@/lib/actions/ticketActions';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Skeleton } from '@/components/ui/skeleton';

const ITEMS_PER_PAGE = 6; // Number of ticket cards per page

const ALL_STATUSES_VALUE = "all_statuses";
const ALL_PRIORITIES_VALUE = "all_priorities";

const TICKET_STATUS_OPTIONS: { value: TicketStatus | typeof ALL_STATUSES_VALUE; label: string }[] = [
  { value: ALL_STATUSES_VALUE, label: "All Statuses" },
  { value: "open", label: "Open" },
  { value: "in_progress", label: "In Progress" },
  { value: "resolved", label: "Resolved" },
  { value: "closed", label: "Closed" },
];


export default function AdminTicketsPage() {
  const [allTickets, setAllTickets] = useState<SupportTicket[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTicketForDialog, setSelectedTicketForDialog] = useState<SupportTicket | null>(null);
  const [availablePriorities, setAvailablePriorities] = useState<{ value: TicketPriority; label: string }[]>([]);
  const [isLoadingPriorities, setIsLoadingPriorities] = useState(true);
  const [isProcessingTicket, setIsProcessingTicket] = useState<string | null>(null);
  const { toast } = useToast();

  const [statusFilter, setStatusFilter] = useState<TicketStatus | typeof ALL_STATUSES_VALUE>(ALL_STATUSES_VALUE);
  const [priorityFilter, setPriorityFilter] = useState<TicketPriority | typeof ALL_PRIORITIES_VALUE>(ALL_PRIORITIES_VALUE);
  const [currentPage, setCurrentPage] = useState(1);

  const fetchAllTickets = async () => {
    setIsLoading(true);
    try {
      const fetchedTickets = await getAllTickets();
      setAllTickets(fetchedTickets);
    } catch (error) {
      toast({
        title: "Error fetching tickets",
        description: error instanceof Error ? error.message : "Could not load ticket data.",
        variant: "error",
      });
      setAllTickets([]);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchPriorities = async () => {
    setIsLoadingPriorities(true);
    try {
      const priorities = await getTicketPriorities();
      setAvailablePriorities(priorities);
    } catch (error) {
      toast({
        title: "Error fetching priorities",
        description: error instanceof Error ? error.message : "Could not load priority options.",
        variant: "error",
      });
    } finally {
      setIsLoadingPriorities(false);
    }
  };

  useEffect(() => {
    fetchAllTickets();
    fetchPriorities();
  }, []);

  const handleUpdateTicketStatus = async (ticketId: string, newStatus: TicketStatus) => {
    setIsProcessingTicket(ticketId);
    const result = await updateTicket(ticketId, { status: newStatus });
    setIsProcessingTicket(null);
    if (result.error || !result.ticket) {
      toast({ title: "Update Failed", description: result.error || "Could not update ticket status.", variant: "error" });
    } else {
      toast({
        title: `Ticket Status Updated`,
        description: `Ticket ${ticketId} is now ${newStatus.replace('_', ' ')}.`,
        variant: "success",
      });
      fetchAllTickets();
    }
  };

  const handleUpdateTicketPriority = async (ticketId: string, newPriority: TicketPriority) => {
    setIsProcessingTicket(ticketId);
    const result = await updateTicket(ticketId, { priority: newPriority });
    setIsProcessingTicket(null);
    if (result.error || !result.ticket) {
      toast({ title: "Update Failed", description: result.error || "Could not update ticket priority.", variant: "error" });
    } else {
      toast({
        title: `Ticket Priority Updated`,
        description: `Ticket ${ticketId} priority is now ${newPriority}.`,
        variant: "success",
      });
      fetchAllTickets();
    }
  };

  const filteredTickets = useMemo(() => {
    return allTickets.filter(ticket => {
      const statusMatch = statusFilter === ALL_STATUSES_VALUE || ticket.status === statusFilter;
      const priorityMatch = priorityFilter === ALL_PRIORITIES_VALUE || ticket.priority === priorityFilter;
      return statusMatch && priorityMatch;
    });
  }, [allTickets, statusFilter, priorityFilter]);

  const totalPages = Math.ceil(filteredTickets.length / ITEMS_PER_PAGE);
  const paginatedTickets = useMemo(() => {
    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    const endIndex = startIndex + ITEMS_PER_PAGE;
    return filteredTickets.slice(startIndex, endIndex);
  }, [filteredTickets, currentPage]);

  const getStatusBadgeVariant = (status?: TicketStatus) => {
    switch (status) {
      case 'open': return 'default';
      case 'in_progress': return 'secondary';
      case 'resolved': return 'outline';
      case 'closed': return 'destructive';
      default: return 'outline';
    }
  };

  const getPriorityBadgeVariant = (priority?: TicketPriority) => {
    switch (priority) {
      case 'low': return 'secondary';
      case 'medium': return 'default';
      case 'high': return 'outline';
      case 'urgent': return 'destructive';
      default: return 'outline';
    }
  };

  const getStatusBorderClass = (status?: TicketStatus): string => {
    switch (status) {
      case 'open': return 'border-primary border-2 shadow-md hover:shadow-lg';
      case 'in_progress': return 'border-accent border-2 shadow-md hover:shadow-lg';
      case 'resolved': return 'border-green-500 border-2 shadow-md hover:shadow-lg';
      case 'closed': return 'border-muted-foreground border-2 opacity-80 shadow-sm hover:shadow-md';
      default: return 'border-border shadow-sm hover:shadow-md';
    }
  };

  const renderSkeletonTicketCards = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {[...Array(ITEMS_PER_PAGE)].map((_, i) => (
        <Card key={i} className="flex flex-col relative shadow-sm border-border">
          <Skeleton className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 h-6 w-20 rounded-full" />
          <CardHeader className="pt-8">
            <div className="flex justify-between items-start">
              <div>
                <Skeleton className="h-5 w-3/4 mb-1" />
                <Skeleton className="h-3 w-1/2" />
              </div>
              <Skeleton className="h-8 w-8 rounded-sm" />
            </div>
          </CardHeader>
          <CardContent className="space-y-4 text-sm flex-grow pt-4">
            {[1, 2, 3, 4].map((j) => (
              <div key={j}>
                <Skeleton className="h-4 w-1/4 mb-1" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            ))}
          </CardContent>
          <CardFooter className="border-t pt-4 flex justify-between items-center">
            <Skeleton className="h-6 w-24" />
          </CardFooter>
        </Card>
      ))}
    </div>
  );


  if (isLoading || isLoadingPriorities) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
          <div className="flex items-center space-x-3">
            <TicketIcon className="h-8 w-8 text-primary" />
            <h1 className="text-3xl font-bold text-primary">Support Tickets</h1>
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-10 w-[180px]" />
            <Skeleton className="h-10 w-[180px]" />
            <Skeleton className="h-10 w-10 rounded-md" />
          </div>
        </div>
        {renderSkeletonTicketCards()}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
        <div className="flex items-center space-x-3">
          <TicketIcon className="h-8 w-8 text-primary" />
          <h1 className="text-3xl font-bold text-primary">Support Tickets</h1>
        </div>
        <div className="flex items-center gap-2">
          <Select value={statusFilter} onValueChange={(value) => { setStatusFilter(value as TicketStatus | typeof ALL_STATUSES_VALUE); setCurrentPage(1); }}>
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Filter by Status" />
            </SelectTrigger>
            <SelectContent>
              {TICKET_STATUS_OPTIONS.map(opt => (
                <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={priorityFilter} onValueChange={(value) => { setPriorityFilter(value as TicketPriority | typeof ALL_PRIORITIES_VALUE); setCurrentPage(1); }}>
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Filter by Priority" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={ALL_PRIORITIES_VALUE}>All Priorities</SelectItem>
              {availablePriorities.map(opt => (
                <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={fetchAllTickets} disabled={isLoading} size="icon" aria-label="Refresh tickets">
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>
      {filteredTickets.length === 0 ? (
        <Card className="shadow-md">
          <CardContent className="text-center py-12">
            <Filter className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-xl font-semibold text-primary mb-2">No Tickets Match Filters</h3>
            <p className="text-muted-foreground">Try adjusting your status or priority filters, or there might be no tickets in the system.</p>
          </CardContent>
        </Card>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {paginatedTickets.map((ticket) => (
              <Card
                key={ticket.id}
                className={cn(
                  "flex flex-col relative transition-shadow duration-200",
                  getStatusBorderClass(ticket.status)
                )}
              >
                <Badge
                  variant={getPriorityBadgeVariant(ticket.priority)}
                  className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 px-3 py-1 text-xs font-semibold rounded-full shadow-lg z-10 capitalize"
                >
                  {availablePriorities.find(p => p.value === ticket.priority)?.label || ticket.priority}
                </Badge>
                <CardHeader className="pt-8">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg font-semibold text-primary">
                        Ticket ID: <span className="font-mono text-base">{ticket.id}</span>
                      </CardTitle>
                      <CardDescription className="text-xs">
                        <User className="inline-block h-3 w-3 mr-1" /> User ID: {ticket.userId}
                      </CardDescription>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8" disabled={isProcessingTicket === ticket.id}>
                          {isProcessingTicket === ticket.id ? <Loader2 className="h-4 w-4 animate-spin" /> : <MoreVertical className="h-4 w-4" />}
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => setSelectedTicketForDialog(ticket)}>
                          <Eye className="mr-2 h-4 w-4" /> View Details
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuSub>
                          <DropdownMenuSubTrigger disabled={isProcessingTicket === ticket.id || isLoadingPriorities}>
                            <Tag className="mr-2 h-4 w-4" />
                            <span>Change Priority</span>
                          </DropdownMenuSubTrigger>
                          <DropdownMenuPortal>
                            <DropdownMenuSubContent>
                              {availablePriorities.map((p) => (
                                <DropdownMenuItem
                                  key={p.value}
                                  onClick={() => handleUpdateTicketPriority(ticket.id, p.value)}
                                  disabled={ticket.priority === p.value || isProcessingTicket === ticket.id}
                                >
                                  {p.label}
                                  {ticket.priority === p.value && <CheckCircle className="ml-auto h-4 w-4 text-primary" />}
                                </DropdownMenuItem>
                              ))}
                            </DropdownMenuSubContent>
                          </DropdownMenuPortal>
                        </DropdownMenuSub>
                        <DropdownMenuSeparator />
                        {ticket.status !== 'in_progress' && (
                          <DropdownMenuItem onClick={() => handleUpdateTicketStatus(ticket.id, 'in_progress')} disabled={isProcessingTicket === ticket.id}>
                            <Zap className="mr-2 h-4 w-4" /> Mark as In Progress
                          </DropdownMenuItem>
                        )}
                        {ticket.status !== 'resolved' && (
                          <DropdownMenuItem onClick={() => handleUpdateTicketStatus(ticket.id, 'resolved')} disabled={isProcessingTicket === ticket.id}>
                            <CheckCircle className="mr-2 h-4 w-4" /> Mark as Resolved
                          </DropdownMenuItem>
                        )}
                        {ticket.status !== 'closed' && (
                          <DropdownMenuItem onClick={() => handleUpdateTicketStatus(ticket.id, 'closed')} disabled={isProcessingTicket === ticket.id}>
                            <XCircle className="mr-2 h-4 w-4" /> Close Ticket
                          </DropdownMenuItem>
                        )}
                        {(ticket.status === 'resolved' || ticket.status === 'closed') && (
                          <DropdownMenuItem onClick={() => handleUpdateTicketStatus(ticket.id, 'open')} disabled={isProcessingTicket === ticket.id}>
                            <RefreshCw className="mr-2 h-4 w-4" /> Re-open Ticket
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4 text-sm flex-grow pt-4">
                  <div>
                    <Label className="flex items-center">
                      <AlignLeft className="mr-1.5 h-4 w-4 text-muted-foreground" /> Subject
                    </Label>
                    <p className="font-semibold text-base truncate text-primary mt-1" title={ticket.subject}>
                      {ticket.subject}
                    </p>
                  </div>
                  <div>
                    <Label className="flex items-center">
                      <Tag className="mr-1.5 h-4 w-4 text-muted-foreground" /> Category
                    </Label>
                    <p className="font-medium text-primary mt-1">{ticket.category}</p>
                  </div>
                  <div>
                    <Label className="flex items-center">
                      <Flag className="mr-1.5 h-4 w-4 text-muted-foreground" /> Priority
                    </Label>
                    <p id={`priority-text-${ticket.id}`} className="font-medium text-primary mt-1 capitalize">
                      {availablePriorities.find(p => p.value === ticket.priority)?.label || ticket.priority}
                    </p>
                  </div>
                  <div>
                    <Label className="flex items-center">
                      <CalendarDays className="mr-1.5 h-4 w-4 text-muted-foreground" /> Last Updated
                    </Label>
                    <p className="font-medium text-primary mt-1">{new Date(ticket.updatedAt).toLocaleString()}</p>
                  </div>
                </CardContent>
                <CardFooter className="border-t pt-4 flex justify-between items-center">
                  <div>
                    <span className="text-xs text-muted-foreground">Status: </span>
                    <Badge variant={getStatusBadgeVariant(ticket.status)} className="capitalize text-xs px-2 py-1">
                      {ticket.status.replace('_', ' ')}
                    </Badge>
                  </div>
                </CardFooter>
              </Card>
            ))}
          </div>
          {totalPages > 1 && (
            <div className="flex items-center justify-center space-x-4 pt-6">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="mr-2 h-4 w-4" /> Previous
              </Button>
              <span className="text-sm text-muted-foreground">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
              >
                Next <ChevronRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          )}
        </>
      )}

      {selectedTicketForDialog && (
        <Dialog open={!!selectedTicketForDialog} onOpenChange={(isOpen) => { if (!isOpen) setSelectedTicketForDialog(null); }}>
          <DialogContent className="sm:max-w-lg">
            <DialogHeader>
              <DialogTitle className="text-primary">Ticket Details - {selectedTicketForDialog.id}</DialogTitle>
              <DialogDescription>
                Full information for support ticket.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4 text-sm">
              <div className="grid grid-cols-[120px_1fr] items-center gap-x-4 gap-y-2">
                <Label htmlFor="ticketIdDialog" className="text-right text-muted-foreground">Ticket ID:</Label>
                <p id="ticketIdDialog" className="font-medium">{selectedTicketForDialog.id}</p>
                <Label htmlFor="userIdDialog" className="text-right text-muted-foreground">User ID:</Label>
                <p id="userIdDialog" className="font-mono text-xs">{selectedTicketForDialog.userId}</p>
                <Label htmlFor="subjectDialog" className="text-right text-muted-foreground">Subject:</Label>
                <p id="subjectDialog" className="font-medium col-span-1">{selectedTicketForDialog.subject}</p>
                <Label htmlFor="categoryDialog" className="text-right text-muted-foreground">Category:</Label>
                <p id="categoryDialog">{selectedTicketForDialog.category}</p>
                <Label htmlFor="priorityDialog" className="text-right text-muted-foreground">Priority:</Label>
                <div id="priorityDialog">
                  <Badge variant={getPriorityBadgeVariant(selectedTicketForDialog.priority)} className="capitalize text-xs font-medium px-2 py-1 rounded-md">
                    {availablePriorities.find(p => p.value === selectedTicketForDialog.priority)?.label || selectedTicketForDialog.priority}
                  </Badge>
                </div>
                <Label htmlFor="statusDialog" className="text-right text-muted-foreground">Status:</Label>
                <div id="statusDialog">
                  <Badge variant={getStatusBadgeVariant(selectedTicketForDialog.status)} className="capitalize">
                    {selectedTicketForDialog.status.replace('_', ' ')}
                  </Badge>
                </div>
                <Label htmlFor="createdAtDialog" className="text-right text-muted-foreground">Created At:</Label>
                <p id="createdAtDialog">{new Date(selectedTicketForDialog.createdAt).toLocaleString()}</p>
                <Label htmlFor="updatedAtDialog" className="text-right text-muted-foreground">Last Updated:</Label>
                <p id="updatedAtDialog">{new Date(selectedTicketForDialog.updatedAt).toLocaleString()}</p>
                {selectedTicketForDialog.resolvedAt && (
                  <>
                    <Label htmlFor="resolvedAtDialog" className="text-right text-muted-foreground">Resolved At:</Label>
                    <p id="resolvedAtDialog">{new Date(selectedTicketForDialog.resolvedAt).toLocaleString()}</p>
                  </>
                )}
              </div>
              <Separator className="my-2" />
              <div>
                <Label htmlFor="descriptionDialog" className="font-medium text-primary">Description:</Label>
                <div id="descriptionDialog" className="mt-1 p-3 bg-muted/50 rounded-md whitespace-pre-wrap break-words">
                  {selectedTicketForDialog.description}
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setSelectedTicketForDialog(null)}>Close</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}

