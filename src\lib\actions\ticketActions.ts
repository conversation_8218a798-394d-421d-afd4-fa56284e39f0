'use server';

import type { SupportTicket, TicketPriority, TicketStatus } from '@/types';
import { logActivity } from '@/lib/actions/activityLogActions';
import { readJsonFile, writeJsonFile, readTicketOptionsFile, type TicketOptionsStructure } from '@/lib/jsonUtils';
import * as z from "zod";

const TICKETS_FILE = 'tickets.json';

const createTicketSchema = z.object({
  userId: z.string().min(1, "User ID is required."),
  subject: z.string().min(5, "Subject must be at least 5 characters.").max(100, "Subject cannot exceed 100 characters."),
  category: z.string({ required_error: "Please select a category." }).min(1, "Please select a category."),
  description: z.string().min(20, "Description must be at least 20 characters.").max(2000, "Description cannot exceed 2000 characters."),
});

type CreateTicketData = z.infer<typeof createTicketSchema>;

export interface TicketActionResult {
  success: boolean;
  ticket?: SupportTicket;
  error?: string;
  fieldErrors?: Partial<Record<keyof CreateTicketData, string[]>>;
}


export async function getTicketCategories(): Promise<string[]> {
  const options = await readTicketOptionsFile();
  return options.categories;
}

export async function getTicketPriorities(): Promise<{ value: TicketPriority; label: string }[]> {
  const options = await readTicketOptionsFile();
  return options.priorities;
}

export async function getUserTickets(userId: string): Promise<SupportTicket[]> {
  if (!userId) {
    return [];
  }
  const tickets = await readJsonFile<SupportTicket>(TICKETS_FILE);
  return tickets.filter(ticket => ticket.userId === userId).sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
}


export async function createTicket(ticketData: CreateTicketData): Promise<TicketActionResult> {
  const validationResult = createTicketSchema.safeParse(ticketData);
  if (!validationResult.success) {
    return {
      success: false,
      error: "Validation failed. Please check the fields below.",
      fieldErrors: validationResult.error.flatten().fieldErrors
    };
  }
  
  const validatedData = validationResult.data;

  try {
    const tickets = await readJsonFile<SupportTicket>(TICKETS_FILE);
    const now = new Date().toISOString();
    const newTicket: SupportTicket = {
      id: `TICKET${Date.now()}${Math.floor(Math.random() * 1000)}`, 
      userId: validatedData.userId,
      subject: validatedData.subject,
      category: validatedData.category,
      priority: 'medium', // Default priority for user-submitted tickets
      description: validatedData.description,
      status: 'open' as TicketStatus,
      createdAt: now,
      updatedAt: now,
    };
    tickets.push(newTicket);
    await writeJsonFile<SupportTicket>(TICKETS_FILE, tickets);

    await logActivity(
      validatedData.userId,
      'TICKET_CREATED',
      `Support ticket '${newTicket.subject.substring(0, 30)}...' created (ID: ${newTicket.id}).`,
      'Ticket'
    );

    return { success: true, ticket: newTicket };
  } catch (error) {
    console.error("Error creating ticket:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to create support ticket.";
    return { success: false, error: errorMessage };
  }
}

export async function countUserTicketsByStatus(userId: string, status: TicketStatus | TicketStatus[]): Promise<number> {
  if (!userId) {
    return 0;
  }
  try {
    const allTickets = await readJsonFile<SupportTicket>(TICKETS_FILE);
    const statusesToCount = Array.isArray(status) ? status : [status];
    return allTickets.filter(ticket => ticket.userId === userId && statusesToCount.includes(ticket.status)).length;
  } catch (error) {
    console.error(`Error counting tickets for user ${userId} with status ${status}:`, error);
    return 0;
  }
}

export async function countAllTicketsByStatus(status: TicketStatus | TicketStatus[]): Promise<number> {
  try {
    const allTickets = await readJsonFile<SupportTicket>(TICKETS_FILE);
    const statusesToCount = Array.isArray(status) ? status : [status];
    return allTickets.filter(ticket => statusesToCount.includes(ticket.status)).length;
  } catch (error) {
    console.error(`Error counting all tickets with status ${status}:`, error);
    // To align with other actions that return a number directly or an object with an error
    // This function should ideally return a number or throw, if it's only for counts.
    // For consistency with how TicketActionResult is structured, if it could fail this way,
    // it might need its own result type. For now, returning 0 on error.
    return 0; 
  }
}


export async function getAllTickets(): Promise<SupportTicket[]> {
  const tickets = await readJsonFile<SupportTicket>(TICKETS_FILE);
  return tickets.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
}

interface UpdateTicketResult {
  success: boolean;
  ticket?: SupportTicket;
  error?: string;
}

export async function updateTicket(ticketId: string, updates: Partial<SupportTicket>): Promise<UpdateTicketResult> {
  let tickets = await readJsonFile<SupportTicket>(TICKETS_FILE);
  const ticketIndex = tickets.findIndex(t => t.id === ticketId);
  if (ticketIndex === -1) {
    return { success: false, error: 'Ticket not found.' };
  }

  const originalTicket = tickets[ticketIndex];
  const now = new Date().toISOString();
  
  tickets[ticketIndex] = {
    ...originalTicket,
    ...updates,
    updatedAt: now,
  };

  if (updates.status && (updates.status === 'resolved' || updates.status === 'closed') && !originalTicket.resolvedAt) {
    tickets[ticketIndex].resolvedAt = now;
  }
  
  if (updates.status && (updates.status === 'open' || updates.status === 'in_progress') && originalTicket.resolvedAt) {
     tickets[ticketIndex].resolvedAt = undefined; 
  }

  await writeJsonFile<SupportTicket>(TICKETS_FILE, tickets);
  return { success: true, ticket: tickets[ticketIndex] };
}

