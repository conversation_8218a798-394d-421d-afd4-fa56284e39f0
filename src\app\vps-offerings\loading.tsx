import { Skeleton } from "@/components/ui/skeleton";

export default function VpsOfferingsLoading() {
  return (
    <div className="space-y-12">
      <section className="text-center py-12">
        <Skeleton className="h-12 w-3/4 md:w-1/2 mx-auto mb-4" />
        <Skeleton className="h-5 w-full md:w-3/4 mx-auto" />
      </section>
      <section>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="rounded-lg border bg-card text-card-foreground shadow-sm flex flex-col h-full">
              <div className="p-6 pt-8">
                <Skeleton className="h-8 w-3/4 mb-2" />
                <Skeleton className="h-4 w-full mb-1" />
                <Skeleton className="h-4 w-5/6 mb-6" />
                <Skeleton className="h-10 w-1/2 mb-6" />
                <div className="space-y-3">
                  {[...Array(4)].map((_, j) => (
                    <div key={j} className="flex items-center">
                      <Skeleton className="w-5 h-5 mr-3 rounded-full" />
                      <Skeleton className="h-4 w-3/4" />
                    </div>
                  ))}
                </div>
                <Skeleton className="h-5 w-1/3 mt-6 mb-2" />
                <div className="space-y-2">
                  {[...Array(2)].map((_, k) => (
                    <div key={k} className="flex items-center">
                      <Skeleton className="w-4 h-4 mr-2 rounded-full" />
                      <Skeleton className="h-4 w-full" />
                    </div>
                  ))}
                </div>
              </div>
              <div className="p-6 mt-auto bg-muted/30">
                <Skeleton className="h-12 w-full" />
              </div>
            </div>
          ))}
        </div>
      </section>
      <section className="py-12 mt-16 bg-secondary/50 rounded-lg p-8">
        <Skeleton className="h-10 w-1/2 mx-auto mb-8" />
        <div className="grid grid-cols-2 md:grid-cols-3 gap-6 max-w-3xl mx-auto text-center">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="p-4 bg-background rounded-md shadow">
              <Skeleton className="h-5 w-full" />
            </div>
          ))}
        </div>
      </section>
    </div>
  );
}
