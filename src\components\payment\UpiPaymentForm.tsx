"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import Image from "next/image";
import { useToast } from "@/hooks/use-toast";
import { Copy, Loader2 } from "lucide-react";
import { useEffect, useState, useCallback } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import type { VPSPlan, Payment } from "@/types";
import { useAuth } from "@/hooks/useAuth";
import { createPaymentRecord } from "@/lib/actions/paymentActions";
import { updateOrderStatusIn<PERSON><PERSON>, createOrder } from "@/lib/actions/orderActions";
import { getVpsPlanById } from "@/lib/actions/vpsPlanActions";


const formSchema = z.object({
  utrNumber: z.string().min(10, { message: "UTR number must be at least 10 characters." }).max(22, { message: "UTR number cannot exceed 22 characters." }),
  selectedPlanId: z.string().optional(),
  orderId: z.string().optional(),
});

const UPI_ID = "skyhosting@upi";
const UNIQUE_CODE_PREFIX = "SKYH";

export function UpiPaymentForm() {
  const { toast } = useToast();
  const searchParams = useSearchParams();
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const [uniquePaymentCode, setUniquePaymentCode] = useState("");
  const [selectedPlan, setSelectedPlan] = useState<VPSPlan | null>(null);
  const [orderIdForPayment, setOrderIdForPayment] = useState<string | null>(null);
  const [isLoadingPlan, setIsLoadingPlan] = useState(true);
  const [isCreatingOrder, setIsCreatingOrder] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      utrNumber: "",
      selectedPlanId: "",
      orderId: "",
    },
  });

  const initializePaymentDetails = useCallback(async () => {
    if (!isAuthenticated || !user) return;

    setIsLoadingPlan(true);
    const planIdFromQuery = searchParams.get('plan') || searchParams.get('planId');
    let orderIdFromQuery = searchParams.get('orderId');

    let currentPlan: VPSPlan | null = null;
    let currentOrderId: string | null = orderIdFromQuery;

    if (planIdFromQuery) {
      const planResult = await getVpsPlanById(planIdFromQuery);
      if (planResult) {
        currentPlan = planResult;
        form.setValue("selectedPlanId", planResult.id);
      } else {
        toast({ title: "Error", description: "Plan details could not be loaded.", variant: "error" });
        setIsLoadingPlan(false);
        return;
      }
    }

    if (planIdFromQuery && !orderIdFromQuery) {
      setIsCreatingOrder(true);
      const orderCreationResult = await createOrder(user.id, planIdFromQuery, user.email);
      setIsCreatingOrder(false);
      if (orderCreationResult.order) {
        currentOrderId = orderCreationResult.order.id;
        toast({ title: "Order Created", description: `Order ${currentOrderId} for ${currentPlan?.name} created.`, variant: "success" });
        router.replace(`/payment/upi?orderId=${currentOrderId}&planId=${planIdFromQuery}`, { scroll: false });
      } else {
        toast({ title: "Order Creation Failed", description: orderCreationResult.error || "Could not create order.", variant: "error" });
        setIsLoadingPlan(false);
        return;
      }
    }

    setSelectedPlan(currentPlan);
    if (currentOrderId) {
      setOrderIdForPayment(currentOrderId);
      form.setValue("orderId", currentOrderId);
    }

    setUniquePaymentCode(`${UNIQUE_CODE_PREFIX}${Math.random().toString(36).substring(2, 8).toUpperCase()}`);
    setIsLoadingPlan(false);

  }, [searchParams, toast, form, user, isAuthenticated, router]);

  useEffect(() => {
    setMounted(true);
    if (isAuthenticated !== null && user !== null) {
      initializePaymentDetails();
    }
  }, [initializePaymentDetails, isAuthenticated, user]);

  async function onSubmit(values: z.infer<typeof formSchema>) {
    if (!isAuthenticated || !user?.id) {
      toast({ title: "Authentication Error", description: "Please log in to submit payment.", variant: "error" });
      return;
    }

    if (!orderIdForPayment && !selectedPlan) {
      toast({ title: "Payment Error", description: "No order or plan selected for payment.", variant: "error" });
      return;
    }

    setIsSubmitting(true);

    const paymentAmount = selectedPlan?.pricePerMonth;
    if (!paymentAmount && !orderIdForPayment) {
      toast({ title: "Payment Error", description: "Could not determine payment amount.", variant: "error" });
      setIsSubmitting(false);
      return;
    }

    const finalOrderId = orderIdForPayment || `PLAN-${selectedPlan?.id || 'UNKNOWN'}`;
    const paymentData = {
      orderId: finalOrderId,
      userId: user.id,
      amount: paymentAmount || 0,
      method: "UPI",
      transactionId: values.utrNumber,
      status: "pending" as Payment['status'],
    };

    const result = await createPaymentRecord(paymentData);

    if (result.error || !result.payment) {
      toast({
        title: "Payment Submission Failed",
        description: result.error || "Could not record your payment. Please try again or contact support.",
        variant: "error",
      });
    } else {
      toast({
        title: "Payment Submitted Successfully",
        description: `Your UTR ${values.utrNumber} for payment code ${uniquePaymentCode} has been recorded. Status: ${result.payment.status}.`,
        duration: 7000,
        variant: "success",
      });

      if (finalOrderId.startsWith("ORD")) {
        const orderUpdateResult = await updateOrderStatusInJson(finalOrderId, 'processing');
        if (orderUpdateResult.success) {
          toast({
            title: "Order Status Updated",
            description: `Order ${finalOrderId} is now processing.`,
            variant: "info",
          });
        } else {
          toast({
            title: "Order Status Update Failed",
            description: orderUpdateResult.error || `Could not update status for order ${finalOrderId}.`,
            variant: "error",
          });
        }
      }
      form.reset({ utrNumber: "", selectedPlanId: selectedPlan?.id, orderId: finalOrderId || "" });
      setUniquePaymentCode(`${UNIQUE_CODE_PREFIX}${Math.random().toString(36).substring(2, 8).toUpperCase()}`);
    }
    setIsSubmitting(false);
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      toast({ title: "Copied to clipboard!", description: text, variant: "info" });
    }).catch(err => {
      toast({ title: "Failed to copy", description: "Could not copy text to clipboard.", variant: "error" });
    });
  };

  if (!mounted || isLoadingPlan || isCreatingOrder || isAuthenticated === null) {
    return (
      <div className="flex items-center justify-center p-12">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">
          {isAuthenticated === null ? "Authenticating..." :
            isCreatingOrder ? "Creating your order..." :
              "Loading payment details..."}
        </span>
      </div>
    );
  }

  if (isAuthenticated === false) {
    return <div className="flex items-center justify-center p-12">Please log in to continue.</div>;
  }

  if (!selectedPlan && (searchParams.get('plan') || searchParams.get('planId')) && !orderIdForPayment) {
    return <div className="flex items-center justify-center p-12 text-destructive">Error: Plan details could not be loaded or order could not be created. Please try again or select a plan from our offerings.</div>;
  }
  
  return (
    <Card className="w-full max-w-2xl mx-auto shadow-xl">
      <CardHeader className="text-center">
        <CardTitle className="text-3xl font-bold text-primary">UPI Payment</CardTitle>
        <CardDescription>
          {selectedPlan
            ? `Complete your payment for the ${selectedPlan.name} plan (₹${selectedPlan.pricePerMonth}).`
            : orderIdForPayment
              ? `Complete your payment for Order ID: ${orderIdForPayment}.`
              : "Scan the QR code or use the UPI ID to make your payment."}
          {orderIdForPayment && (!selectedPlan || selectedPlan.id !== form.getValues().selectedPlanId) &&
            ` (Order ID: ${orderIdForPayment})`}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-8">
        <div className="flex flex-col md:flex-row items-center md:items-start gap-8">
          <div className="flex flex-col items-center space-y-4">
            <div className="p-4 border rounded-lg bg-white">
              <Image
                src="https://placehold.co/200x200.png?text=Scan+Me"
                alt="UPI QR Code"
                width={200}
                height={200}
                data-ai-hint="qrcode payment"
                className="rounded-md"
              />
            </div>
            <Button variant="outline" onClick={() => copyToClipboard(UPI_ID)}>
              <Copy className="mr-2 h-4 w-4" /> Copy UPI ID: {UPI_ID}
            </Button>
          </div>
          <div className="flex-1 space-y-4 text-center md:text-left">
            <h3 className="text-lg font-semibold text-primary">Payment Instructions:</h3>
            <ol className="list-decimal list-inside text-muted-foreground space-y-1 text-sm">
              <li>Scan the QR code or use the UPI ID <strong className="text-foreground">{UPI_ID}</strong>.</li>
              {selectedPlan && <li>Enter amount: <strong className="text-foreground">₹{selectedPlan.pricePerMonth}</strong>.</li>}
              {!selectedPlan && orderIdForPayment && <li>Please ensure you enter the correct amount for your order.</li>}
              {!selectedPlan && !orderIdForPayment && <li>Please ensure you enter the correct amount for your service.</li>}
              <li>
                <strong>Important:</strong> Add this unique code in the payment remarks/notes:
                <div className="my-2 p-2 bg-accent/10 text-accent font-mono text-lg rounded-md flex items-center justify-center md:justify-start">
                  <span>{uniquePaymentCode}</span>
                  <Button variant="ghost" size="icon" className="ml-2 h-6 w-6" onClick={() => copyToClipboard(uniquePaymentCode)}>
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </li>
              <li>Complete the payment through your UPI app.</li>
              <li>After payment, enter the UTR/Transaction ID below.</li>
            </ol>
          </div>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 pt-6 border-t">
            <FormField
              control={form.control}
              name="utrNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-lg font-semibold text-primary">Enter UTR Number</FormLabel>
                  <FormControl>
                    <Input placeholder="12-digit UTR / Transaction ID" {...field} className="text-base py-3 px-4" disabled={isSubmitting} />
                  </FormControl>
                  <FormDescription>
                    This is the reference number from your UPI transaction.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit" className="w-full" size="lg" disabled={isSubmitting || !isAuthenticated}>
              {isSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
              {isSubmitting ? "Submitting..." : "Submit UTR & Confirm Payment"}
            </Button>
            {!isAuthenticated && <p className="text-sm text-destructive text-center">Please log in to submit payment.</p>}
          </form>
        </Form>
      </CardContent>
      <CardFooter>
        <p className="text-xs text-muted-foreground text-center w-full">
          Please ensure you enter the correct UTR number and unique payment code. Verification may take some time.
        </p>
      </CardFooter>
    </Card>
  );
}
