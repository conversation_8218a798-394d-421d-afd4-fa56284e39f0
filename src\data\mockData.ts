import type { SupportTicket } from '@/types';

// mockVpsPlans has been moved to src/database/vps-plans.json
// and will be fetched via server actions in src/lib/actions/vpsPlanActions.ts

// mockSupportTickets is no longer the source of truth for the support page.
// It is being kept here temporarily in case other parts of the app (not yet refactored)
// might still be referencing it. Once all ticket-related features use tickets.json,
// this can be fully removed.
export const mockSupportTickets: SupportTicket[] = [
  {
    id: 'TICKET001',
    userId: '1', // Corresponds to '<EMAIL>'
    subject: 'Cannot connect to my VPS',
    description: 'I deployed a new Developer VPS (ORD001) yesterday, and I am unable to SSH into it. I have tried resetting the root password from the panel, but it still does not work. My IP is ************.',
    category: 'Technical Issue',
    priority: 'high',
    status: 'open',
    createdAt: new Date(Date.now() - ******** * 2).toISOString(), // 2 days ago
    updatedAt: new Date(Date.now() - ********).toISOString(), // 1 day ago
  },
  {
    id: 'TICKET002',
    userId: '1',
    subject: 'Question about billing for ORD002',
    description: 'I see a pending payment for order ORD002 for the Starter VPS. Can I pay this via bank transfer instead of UPI/Card?',
    category: 'Billing Inquiry',
    priority: 'medium',
    status: 'in_progress',
    createdAt: new Date(Date.now() - ******** * 1).toISOString(), // 1 day ago
    updatedAt: new Date(Date.now() - 3600000 * 5).toISOString(), // 5 hours ago
  },
  {
    id: 'TICKET003',
    userId: 'admin001', // Corresponds to '<EMAIL>'
    subject: 'Feature Request: Dark Mode for Admin Panel',
    description: 'The admin panel is great, but a dark mode option would be fantastic for my eyes, especially when working at night. Thanks!',
    category: 'Feature Request',
    priority: 'low',
    status: 'resolved',
    createdAt: new Date(Date.now() - ******** * 5).toISOString(), // 5 days ago
    updatedAt: new Date(Date.now() - ******** * 3).toISOString(), // 3 days ago
    resolvedAt: new Date(Date.now() - ******** * 3).toISOString(),
  },
  {
    id: 'TICKET004',
    userId: '1',
    subject: 'Server running slow after update',
    description: 'My Business VPS (ORD003) seems to be running much slower after I performed a system update last night. Web pages are taking a long time to load.',
    category: 'Performance Issue',
    priority: 'high',
    status: 'open',
    createdAt: new Date(Date.now() - 3600000 * 3).toISOString(), // 3 hours ago
    updatedAt: new Date(Date.now() - 3600000 * 1).toISOString(), // 1 hour ago
  },
];

// ticketCategories and ticketPriorities arrays removed from here.
// They are now sourced from src/database/ticket-options.json via server actions.
