export interface User {
  id: string;
  email: string;
  name?: string;
  is2FAEnabled?: boolean;
  role?: 'admin' | 'user';
  status?: 'active' | 'suspended' | 'pending_verification';
}

// Renamed to avoid conflict with DOM Session type if ever used.
export interface AppSession {
  sessionId: string;
  userId: string;
  expiresAt: string; // ISO Date string
  // Consider adding userAgent or IP for basic validation later
}

export interface VPSPlan {
  id: string;
  name: string;
  description: string;
  pricePerMonth: number;
  originalPricePerMonth?: number;
  discountLabel?: string;
  specialTag?: 'Most Bought' | 'Best Price' | 'New' | 'Recommended' | 'Sale';
  cpuCores: number;
  ramGB: number;
  storageGB: number;
  bandwidthTB: number;
  features: string[];
}

export interface FraudRiskAnalysisInput {
  userData: {
    userId: string;
    email: string;
    billingAddress: string;
    paymentMethod: string;
    orderHistory?: string[];
  };
  orderData: {
    orderId: string;
    vpsPlan: string;
    orderDate: string;
    ipAddress: string;
    totalAmount: number;
  };
}

export interface FraudRiskAnalysisOutput {
  isFraudulent: boolean;
  fraudRiskScore: number;
  riskFactors: string[];
  recommendation: string;
}

export interface Order {
  id: string;
  userId: string;
  userEmail?: string;
  planId: string;
  vpsPlanName?: string;
  orderDate: string; // ISO Date string
  status: 'pending' | 'active' | 'cancelled' | 'fraud_review' | 'processing';
  totalAmount: number;
  paymentMethod: string;
  billingAddress?: string;
  ipAddress?: string; // This will serve as the VPS IP
  fraudAnalysis?: FraudRiskAnalysisOutput & { analyzedAt?: string };
  vpsStatus?: 'running' | 'stopped' | 'rebooting' | 'suspended' | 'provisioning' | 'error';
  operatingSystem?: string;
}

export interface Payment {
  id: string;
  orderId: string;
  userId: string;
  paymentDate: string; // ISO Date string
  amount: number;
  method: string; // e.g., 'UPI'
  transactionId?: string; // e.g., UTR number for UPI
  status: 'pending' | 'completed' | 'failed';
}

// Support Ticket System
export type TicketStatus = 'open' | 'in_progress' | 'resolved' | 'closed';
export type TicketPriority = 'low' | 'medium' | 'high' | 'urgent';

export interface SupportTicket {
  id: string;
  userId: string;
  subject: string;
  description: string;
  category: string; // e.g., 'Billing', 'Technical', 'General Inquiry'
  priority: TicketPriority;
  status: TicketStatus;
  createdAt: string; // ISO Date string
  updatedAt: string; // ISO Date string
  resolvedAt?: string; // ISO Date string
}

// Theme System Types
export interface ThemeSettings {
  background: string;
  foreground: string;
  primary: string;
  primaryForeground: string;
  secondary: string;
  secondaryForeground: string;
  accent: string;
  accentForeground: string;
}

export interface AppTheme {
  id: string;
  name: string;
  settings: ThemeSettings;
}

// Activity Log
export interface ActivityLogEntry {
  id: string;
  userId: string;
  timestamp: string; // ISO Date string
  type: 'ORDER_PLACED' | 'PAYMENT_COMPLETED' | 'TICKET_CREATED' | 'VPS_STARTED' | 'VPS_STOPPED' | 'PROFILE_UPDATED' | 'LOGIN_SUCCESS' | 'GENERIC_NOTIFICATION';
  description: string;
  icon?: string; // Optional: lucide-react icon name string
}
