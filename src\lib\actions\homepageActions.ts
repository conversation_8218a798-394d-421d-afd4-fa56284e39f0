'use server';

import type { RawSlideData } from '@/components/home/<USER>';
import { readJsonFile } from '@/lib/jsonUtils';

const HOMEPAGE_SLIDES_FILE = 'homepage-slides.json';

export async function getSlideshowData(): Promise<RawSlideData[]> {
  try {
    const slides = await readJsonFile<RawSlideData>(HOMEPAGE_SLIDES_FILE);
    return slides;
  } catch (error) {
    console.error('Error in getSlideshowData:', error);
    return [];
  }
}
